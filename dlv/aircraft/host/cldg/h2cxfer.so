#define DPSTART  0x4000000
#define DPSTART1 0x5000000
static struct flt_shm_t  *flt                             = (struct flt_shm_t *)(0x0001ea3c +DPSTART);     ///<flt_shm_t,$SIMH/bin/flt_shm.struct        
static struct cldg_shm_t  *cldg                            = (struct cldg_shm_t *)(0x000dbba0 +DPSTART);     ///<cldg_shm_t,$SIMH/include/cldg_shm.struct  
static struct h2c_xfer_t  *h2c                             = (struct h2c_xfer_t *)(0x0115d050 +DPSTART);     ///<h2c_xfer_t,$SIMH/include/xfer_icd.struct  
static struct c2h_xfer_t  *c2h                             = (struct c2h_xfer_t *)(0x0115d62c +DPSTART);     ///<c2h_xfer_t,$SIMH/include/xfer_icd.struct  
static struct ctl_shm_t  *ctl                             = (struct ctl_shm_t *)(0x0002c114 +DPSTART);     ///<ctl_shm_t,$SIMH/bin/ctl_shm.struct        
static struct atst_shm_t  *atst                            = (struct atst_shm_t *)(0x00007530 +DPSTART);     ///<atst_shm_t,$SIMH/bin/atst_shm.struct      
static struct hyd_shm_t  *hyd                             = (struct hyd_shm_t *)(0x0003690c +DPSTART);     ///<hyd_shm_t,$SIMH/bin/hyd_shm.struct        
static struct af_shm_t  *af                              = (struct af_shm_t *)(0x00019c1c +DPSTART);     ///<af_shm_t,$SIMH/bin/af_shm.struct          
