################################################################################
# %OPLICENSE%                                                                  *
#             RESTRICTED PROPRIETARY SOFTWARE AND DATA                         *
#                                                                              *
# This software and the data incorporated herein is licensed, not sold,        *
# and is protected by Federal and State copyright and related intellectual     *
# property laws. It may not be disclosed, copied, reversed engineered or used  *
# in any manner except in strict accordance with the terms and conditions of   *
# a certain License granted by OPINICUS to FLYRIGHT HOLDINGS INC. and only on  *
# the one King Air B350/200 simulator to which it relates. Sale, lease or      *
# other transfer of the simulator does not authorize the transferee to use     *
# this software and the data incorporated herein unless strict compliance with *
# the terms of the License referenced above has been met. A copy of the        *
# License is available from OPINICUS upon request. Third party licensed        *
# proprietary data may also be incorporated herein and is subject to the       *
# terms of those licenses.                                                     *
# %OPLICENSE%                                                                  # 
################################################################################
LDIR = atst
include ../exec/Makefile.rules

CFLAGS += -D__REV__=$(DREV)

LIBATST = lib$(ACDES)atst.a$(LINUXVERSION)

all : $(LIBATST)

OBJS =  atst_io.o    \
        atst_main.o   

OBJ_SO = $(OBJS:%.o=%.so)

LIBFLT_OBJS = $(OBJS:%.o=$(LIBATST)(%.o))

$(LIBATST) : $(LIBFLT_OBJS)
	ln -fs $(LIBATST) lib$(ACDES)atst.a

clean : 
	rm -f lib$(ACDES)atst* *.o *.so .depend

op : 
	make -f makefile_op

clean_op : 
	make -f makefile_op clean

.INTERMEDIATE : $(OBJS) $(OBJ_SO)

# Automatic dependency rules
.depend: $(wildcard $(patsubst %,%/*.c,$(subst :, ,$(VPATH)))) makefile
	$(CC) $(CFLAGS) -MM -MG $(filter-out makefile, $^)  | \
	/usr/bin/gawk '{sub(/:/,": \044{FRC} \044{FRC_SO}");print}' > .depend

include .depend
