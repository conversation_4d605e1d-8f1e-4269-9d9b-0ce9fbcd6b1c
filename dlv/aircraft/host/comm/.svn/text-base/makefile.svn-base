################################################################################
# %OPLICENSE%                                                                  *
#             RESTRICTED PROPRIETARY SOFTWARE AND DATA                         *
#                                                                              *
# This software and the data incorporated herein is licensed, not sold,        *
# and is protected by Federal and State copyright and related intellectual     *
# property laws. It may not be disclosed, copied, reversed engineered or used  *
# in any manner except in strict accordance with the terms and conditions of   *
# a certain License granted by OPINICUS to FLYRIGHT HOLDINGS INC. and only on  *
# the one King Air B350/200 simulator to which it relates. Sale, lease or      *
# other transfer of the simulator does not authorize the transferee to use     *
# this software and the data incorporated herein unless strict compliance with *
# the terms of the License referenced above has been met. A copy of the        *
# License is available from OPINICUS upon request. Third party licensed        *
# proprietary data may also be incorporated herein and is subject to the       *
# terms of those licenses.                                                     *
# %OPLICENSE%                                                                  # 
################################################################################
LDIR = comm
include ../exec/Makefile.rules

CFLAGS += -D__REV__=$(DREV)

LIBCOMM = lib$(ACDES)comm.a$(LINUXVERSION)

all : $(LIBCOMM)


OBJS =  comm_main.o         \
	comm_rcvr.o         \
	vhf_comm_rcvr.o     \
	hf_comm_rcvr.o      \
	atis_infc.o         \
	crew_comm.o         \
	crew_comm_infc.o    \
	inst_comm_infc.o    \
	comm_misc.o         \
	comm_misc_infc.o    \
        comm_io.o        

OBJ_SO = $(OBJS:%.o=%.so)

LIBCOMM_OBJS = $(OBJS:%.o=$(LIBCOMM)(%.o))

$(LIBCOMM) : $(LIBCOMM_OBJS)
	ln -fs $(LIBCOMM) lib$(ACDES)comm.a

clean : 
	rm -f lib* *.o *.so .depend

.INTERMEDIATE : $(OBJS) $(OBJ_SO)

# Automatic dependency rules
.depend: $(wildcard $(patsubst %,%/*.c,$(subst :, ,$(VPATH)))) makefile
	$(CC) $(CFLAGS) -MM -MG $(filter-out makefile, $^)  | \
	/usr/bin/gawk '{sub(/:/,": \044{FRC} \044{FRC_SO}");print}' > .depend

include .depend
