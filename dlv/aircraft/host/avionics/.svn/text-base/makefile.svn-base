################################################################################
# %OPLICENSE%                                                                  *
#             RESTRICTED PROPRIETARY SOFTWARE AND DATA                         *
#                                                                              *
# This software and the data incorporated herein is licensed, not sold,        *
# and is protected by Federal and State copyright and related intellectual     *
# property laws. It may not be disclosed, copied, reversed engineered or used  *
# in any manner except in strict accordance with the terms and conditions of   *
# a certain License granted by OPINICUS to FLYRIGHT HOLDINGS INC. and only on  *
# the one King Air B350/200 simulator to which it relates. Sale, lease or      *
# other transfer of the simulator does not authorize the transferee to use     *
# this software and the data incorporated herein unless strict compliance with *
# the terms of the License referenced above has been met. A copy of the        *
# License is available from OPINICUS upon request. Third party licensed        *
# proprietary data may also be incorporated herein and is subject to the       *
# terms of those licenses.                                                     *
# %OPLICENSE%                                                                  # 
################################################################################
LDIR = avionics
include ../exec/Makefile.rules

CFLAGS += -D__REV__=$(DREV)

BASE =  lib$(ACDES)avi.a

LIB = $(BASE)$(LINUXVERSION)

all : $(LIB)

OBJS =  av_main.o      \
        fi_io.o \
        oat_probe.o \
        oat_indicator.o \
        mag_compass.o \
        pitot_head.o \
        static_ports.o \
        pilot_air_src_valve.o \
        pitot_static_sys.o \
        fi_main.o \
        av_io.o \
        gps_4000.o \
        transponder.o \
        avionics_switching.o \
        adc_sab_io.o \
        dcu_sab_io.o \
        edc_sab_io.o \
        fgc_sab_io.o \
        nav_sab_io.o \
        dme_sab_io.o \
        ahrs_sab_io.o \
        esis_sab_io.o \
        taws_sab_io.o \
        wxr_sab_io.o \
        ralt_sab_io.o \
        pl21_sab_io.o 

OBJ_SO = $(OBJS:%.o=%.so)

LIB_OBJS = $(OBJS:%.o=$(LIB)(%.o))

$(LIB) : $(LIB_OBJS)
	ln -fs $(LIB) $(BASE)

DEPEND = .depend

clean : 
	rm -f $(LIB) *.o *.so $(DEPEND)

op: 
	make -f makefile_op

clean_op: 
	make -f makefile_op clean

.INTERMEDIATE : $(OBJS) $(OBJ_SO)

# Automatic dependency rules
$(DEPEND): $(wildcard $(patsubst %,%/*.c,$(subst :, ,$(VPATH)))) makefile
	$(CC) $(CFLAGS) -MM -MG $(filter-out makefile, $^)  | \
	/usr/bin/gawk '{sub(/:/,": \044{FRC} \044{FRC_SO}");print}' > $@

include $(DEPEND)
