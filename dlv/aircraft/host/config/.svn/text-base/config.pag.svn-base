********************************************************************************
* %OPLICENSE%                                                                  *
*             RESTRICTED PROPRIETARY SOFTWARE AND DATA                         *
*                                                                              *
* This software and the data incorporated herein is licensed, not sold,        *
* and is protected by Federal and State copyright and related intellectual     *
* property laws. It may not be disclosed, copied, reversed engineered or used  *
* in any manner except in strict accordance with the terms and conditions of   *
* a certain License granted by OPINICUS to FLYRIGHT HOLDINGS INC. and only on  *
* the one King Air B350/200 simulator to which it relates. Sale, lease or      *
* other transfer of the simulator does not authorize the transferee to use     *
* this software and the data incorporated herein unless strict compliance with *
* the terms of the License referenced above has been met. A copy of the        *
* License is available from OPINICUS upon request. Third party licensed        *
* proprietary data may also be incorporated herein and is subject to the       *
* terms of those licenses.                                                     *
* %OPLICENSE%                                                                  * 
********************************************************************************
log load
color off
ac off
case lower
command off
#data reset
c elec->busoveride 0 
c elec->cboveride 0 
# enable 28v for tripping cb's
c sio->LXDO020101 1
# turn on in operation light
c sio->LXDO020119 1
c pw_egpws_data->egpws_debug 1
*
p '../config/b350_table.pag'
p '../config/ser_cfg.pag'
*
*
* load EGPWS tables
p '../config/egpws_table.pag'
*
* Vibe Gains
mx->vib_ena = 1
flt->fv.eng_ovrall_gn  = 20.0
#flt->fv.gear_ovrall_gn = 1.5
flt->fv.gear_ovrall_gn = 4.0
# flt->fv.stall_ovrall_gn = 0.4
flt->fv.stall_ovrall_gn = 2.8
#flt->fv.flap_ovrall_gn = 1.0
flt->fv.flap_ovrall_gn = 3.0
#flt->fv.gear_lock_val[:] = 0.04
flt->fv.gear_lock_val[:] = 0.02
flt->fv.beta_ovrall_gn = 0.3
flt->fv.rwy_cl_bump_gn = 0.03
flt->fv.rwy_tarstrp_bump_gn = 0.003
flt->fv.rwy_rghrwy_ovrall_gn = 0.2 
flt->fnscuffm = 1
log off
