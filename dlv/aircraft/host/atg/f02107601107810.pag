################################################################################
* %OPLICENSE%                                                                  *
*             RESTRICTED PROPRIETARY SOFTWARE AND DATA                         *
*                                                                              *
* This software and the data incorporated herein is licensed, not sold,        *
* and is protected by Federal and State copyright and related intellectual     *
* property laws. It may not be disclosed, copied, reversed engineered or used  *
* in any manner except in strict accordance with the terms and conditions of   *
* a certain License granted by OPINICUS to FLYRIGHT HOLDINGS INC. and only on  *
* the one King Air B350/200 simulator to which it relates. Sale, lease or      *
* other transfer of the simulator does not authorize the transferee to use     *
* this software and the data incorporated herein unless strict compliance with *
* the terms of the License referenced above has been met. A copy of the        *
* License is available from OPINICUS upon request. Third party licensed        *
* proprietary data may also be incorporated herein and is subject to the       *
* terms of those licenses.                                                     *
* %OPLICENSE%                                                                  *
################################################################################
#
#  TITLE:       Pitch Trim Rate
#  -----
#
#  PROJECT:     BEECHCRAFT KING AIR 350
#  -------
#
#  AIRCRAFT:    B3501
#  --------
#
#  CRITERIA:    2.a.7
#  --------
#
#  TEST NUMBER: f02107601107810
#  -----------
#
##############################################
#
#  PRELIMINARY
#  -----------
#
# FAA:               2.a.7
# Maneuver:          Pitch Trim Rate
# File No:           k3602011_cmb.csv
# Flight Test Date:  01/27/01
# Start FT_Time:     0.00
# Stop  FT_Time:     158.98
# Start OP_Time:     35.00
# Stop  OP_Time:     139.00
# -------------------------------------
# Flap:              35.00
# Gear:              1.00
# Weight:            14262.4
# Hp:                22940.8
# Kcas:              117.57
# Mach:              0.2764
# TAT:               -41.875
#
WAIT 1
C atst->kdatstno 2107601107810
#
strcpy atst->project   'Beechcraft B350 Training Device - v.'
strcpy atst->aircraft  'B350'
strcpy atst->tst_title 'Pitch Trim Rate, Air'
strcpy atst->faa_tstno '2.a.7.b'
strcpy atst->eng_type  'PT6A-60A'
strcpy atst->ft_file   'k3602011_cmb.csv'
#
atst->ft_time_hack[0]=35.0
atst->ft_time_hack[1]=139.0
#
WAIT 1
#
# Include Flight Test Trim Values
# -------------------------------
p '$AUTO/atg/ft_f02107601107810.pag'
#
WAIT RESET
WAIT DEFAULT 1000
AUTOCHANGE NO
WAIT 1
WAIT sim->config_inprogress F
flt->frz=T
# flt->fmwtcg=T
flt->nposfrz=T
flt->ftgactiv=T
atst->lixindis=T
#
#  PARAMETER PLOT
#  --------------
#
PLOT BEGIN
PLOT POINTS
PLOT TITLE 'Pitch Trim Rate, Air'
PLOT FILE 'f02107601107810.PLT'
P '$AUTO/plot/f02107601107810.plt'
PLOT END
#
#  LFI TABLES
#  ----------
#
LFI RESET
P '$AUTO/lfi_atg/f02107601107810.lfi'
#
##############################################
#
#  WEIGHT AND BALANCE
#  ------------------
#
# Total Fuel Load divided evenly between left and right
fuel->fdemtnk[0]= 1425.2  # Left Total  = 1423.9 (Main=1241.3,Aux=182.6)
fuel->fdemtnk[1]= 1425.2  # Right Total = 1426.5 (Main=1243.9,Aux=182.6)
fuel->fsetfuel=T
flt->fdemgw=     14262.4  # Gross Weight (lbs)
flt->fsetgw=T
wait 20 flt->fwgrss 14062.4 14462.4
flt->dmd_cg_in=202.661     # Center of Gravity (in)
flt->fsetcg=T
flt->fsetgw=T
flt->fdemixx=     30575.5  # Ixx (slug-ft^2)
flt->fdemiyy=     20872.6  # Iyy (slug-ft^2)
flt->fdemizz=     48059.4  # Izz (slug-ft^2)
flt->fdemixz=      1808.3  # Ixz (slug-ft^2)
# flt->fsetiner=T            # Set Inertias
#
#  ENVIRONMENT
#  -----------
#
flt->fdembaro=  29.92  # barometric pressure
WAIT 1
flt->fsetbaro=T
flt->fdemtemp= -45.21  # oat (deg C)
WAIT 1
flt->fsetoat=T
flt->fdemwdir=   0.00  #    0.00 # Wind Direction
flt->fdemwsp=    0.00  #    0.00 # Wind Speed
flt->fsetwdir=T
flt->fsetwsp=T
flt->fdemrwc=0         # runway conditions, dry
flt->fsetrwc=T
#
#  FLIGHT CONDITIONS
#  -----------------
#
flt->fdemhter=0.0      # 0.0 field elevation (ft)
flt->fsethter=T
flt->fdemalt=22940.8    # alt (ft)
flt->fsetalt=1
wait 20 flt->fhp 22890.8 22990.8
flt->fdemhter=0.0      # 0.0 field elevation (ft)
flt->fsethter=T
atst->rzcalthp=22940.8   # demanded alt (ft)
#
flt->rtcvc=    117.57   # vc (kts)
flt->rzcroc=     17.47   # roc (fpm)
flt->rfxpitcm=    2.32   # theta (deg)
flt->rfxrolcm=   -1.64   # phi (deg)
flt->rfxhdgcm= 291.34+   0.00   # hdg (deg)
#
#  AIRCRAFT CONFIGURATION
#  ----------------------
#
ctl->fsetflap=T
ctl->fdemflap=   2.00  # flap (deg)
ctl->fsetgear=T
ctl->fdemgear=   1.00  # gear
wait 20 ctl->gear.avg_pos  0.90  1.10
wait 20 ctl->flap.avg_surf_pos 34.50 35.50 # (deg)
#
flt->fdemstab=    6.161   #   6.161 longitudinal trim pos.
atst->fdemrtab=   0.000   #   3.335 rudder trim pos.
atst->fdematab=   0.000   #   2.542 aileron trim pos.
atst->fsetstab=T
atst->fsetrtab=T
atst->fsetatab=T
atst->fsetydmp=F  # Yaw Damper
ctl->fsetstabsw=T
#
#  ENGINES
#  -------
#
flt->rzcn1[0]=      93.508  # Engine 1 N1 (perc)
flt->rzcn1[1]=      93.529  # Engine 2 N1 (perc)
atst->rzcn2[0]=   1501.868  # Engine 1 N2 (perc)
atst->rzcn2[1]=   1508.192  # Engine 2 N2 (perc)
eng->dmd_prla[0]=   17.37   #    16.98 Engine 1 PROPELLER LEVER ANGLE (deg)
eng->dmd_prla[1]=   17.37   #    17.76 Engine 2 PROPELLER LEVER ANGLE (deg)
eng->dmd_cl[0]=     20.00   #    19.71 Engine 1 CONDITION LEVER ANGLE (deg)
eng->dmd_cl[1]=     20.00   #    20.29 Engine 2 CONDITION LEVER ANGLE (deg)
eng->fdemeng[0]=    55.39   #    54.63 Engine 1 TLA (deg)
eng->fdemeng[1]=    55.39   #    56.16 Engine 2 TLA (deg)
atst->rzctq[0]=   2134.92   #  2122.72 Engine 1 Torque (ft-lb)
atst->rzctq[1]=   2134.92   #  2147.12 Engine 2 Torque (ft-lb)
eng->ebstart[:]=1           # Faststart both engines
wait 1
#
#  SYSTEMS
#  -------
#
hyd.In.di_park_brake=F
atst->fdemtoeb=   0.00  # Cmd Brake Pos
atst->fdemtoer=   0.08  # Cmd Brake Pos
atst->fsettoeb=T
atst->fsettoer=T
atst->rzcbp[0]=   0.00  # Cmd Brk Press
atst->rzcbp[1]=  13.40  # Cmd Brk Press
#
##############################################
#
#  TRIMMER
#  -------
#
atst->kaxpmode=4        # PRIMARY CONTROLS PITCH MODE
atst->kaxrmode=4        # PRIMARY CONTROLS ROLL MODE
atst->kaxymode=4        # PRIMARY CONTROLS YAW MODE
atst->rdtrtime=30.00    # TIME WAIT ELAPSED
atst->ldatrmon(1)=T     # TRIM TIMER
atst->ldatrmon(2)=F     #
atst->ldatrmon(3)=F     #
atst->ldatrmon(4)=T     # ROC
atst->ldatrmon(5)=F     #
atst->ldatrmon(6)=T     # TRANSLATIONAL ACCEL
atst->ldatrmon(7)=T     # ROTATIONAL ACCEL
atst->ldatrmon(8)=F     # ROTATIONAL RATES, 0 IF COMMANDING BANK ANGLE
atst->ldatrmon(9)=F     # LONGITUDINAL CONTROL FORCE
atst->ldatrmon(10)=F    #
atst->ldatrmon(11)=F    #
atst->ldatrmon(12)=T    # GROSS WEIGHT
atst->ldatrmon(13)=T    # x CG
atst->ldatrmon(14)=F    #
atst->ldatrmon(15)=F    #
flt->ifxtrim=3          # TRIM TYPE
#
flt->rzcudot=   0.000   # udot (ft/sec**2)    -0.013
flt->rzcvdot=   0.000   # vdot (ft/sec**2)    -1.068
flt->rzcwdot=   0.000   # wdot (ft/sec**2)   -32.103
flt->rzcpdot=   0.000   # pdot (rad/sec**2)    0.000
flt->rzcqdot=   0.000   # qdot (rad/sec**2)    0.000
flt->rzcrdot=   0.000   # rdot (rad/sec**2)    0.000
flt->rzcpds =   0.000   # p (deg/sec)          0.069
flt->rzcqds =   0.000   # q (deg/sec)          0.059
flt->rzcrds =   0.000   # r (deg/sec)         -0.216
#
atst->lzsslip=F         # STEADY SIDSLIP TRIM
flt->lfxstabt=F
atst->lfxailt=F
atst->lfxrudt=F
#
#  DRIVER
#  ------
#
#
atst->lzaoahld=F        #  AP AOA HOLD FLAG
atst->lzaoacf=F         #  AOA HOLD WITH CL SHIFT
atst->lzpithld=T        #  AP PITCH HOLD FLAG
atst->lzpitalthld=F     #  AP PITCH HOLD WITH ALTITUDE BIAS FLAG
atst->lzpitiashld=F     #  AP PITCH HOLD WITH IAS BIAS FLAG
atst->lzpitrochld=F     #  AP PITCH HOLD WITH ROC BIAS FLAG
atst->lzpassist=F       #  PITCH DRIVE ASSIST FLAG
atst->lzpitcf=F         #  PITCH HOLD WITH CM SHIFT
atst->lzcradalt=F       #  CMD ALT IS RAD ALT
atst->lzrochld=F        #  AP ROC HOLD FLAG
atst->lzrolhld=T        #  AP ROLL HOLD FLAG
atst->lzrassist=F       #  ROLL DRIVE ASSIST FLAG
atst->lzrolcf=F         #  ROLL HOLD WITH CR SHIFT
atst->lzbethld=F        #  AP BETA HOLD FLAG
atst->lzyassist=F       #  YAW DRIVE ASSIST FLAG
atst->lzbetcf=F         #  BETA HOLD WITH YAW MOMENT
atst->lzhdghld=T        #  AP HDG HOLD WITH RUD FLAG
atst->lzhdgcf=F         #  HEADING HOLD WITH CN SHIFT
atst->lzrwyhld=F        #  RUNWAY HEADING HOLD
atst->lzrwycf=F         #  RUNWAY HOLD WITH YAW MOMENT
atst->lzgamhld=F        #  GAMMA HOLD
atst->lznycf=F          #  NY HOLD WITH YAW MOMENT
atst->lziashld=F        #  AP IAS HOLD FLAG
atst->lziasphd=F        #  IAS HOLD VIA PITCH A/P FLAG
atst->lziascf=F         #  IAS HOLD WITH CD SHIFT
atst->lzmachld=F        #  AP MACH HOLD FLAG
atst->lzfnhld=F         #  AP FN HOLD FLAG
atst->lzfnudothld=F     #  FN HLD WITH UDOT BIAS
atst->lzfniashld=F      #  FN HLD WITH IAS BIAS
atst->lzn1hld=F         #  AP N1 HOLD FLAG
flt->lztqhld=F          #  TORQUE HOLD
atst->lztqudothld=F     #  TORQUE HOLD WITH UDOT BIAS
atst->lztassist=F       #  TORQUE HOLD THORTTLE ASSIST
atst->lzeprhld=F        #  AP EPR HOLD FLAG
atst->lzreverse(1)=F    #  REVERSE THRUST FLAG
atst->lzreverse(2)=F    #  REVERSE THRUST FLAG
atst->lzbrknx=F         #  NX BRAKE HOLD
atst->lzbphld=F         #  BRAKE PRESS HOLD WITH PEDAL
atst->lzflare=F         #  FLARE MODE
#
#  BEGIN TEST
#  ----------
#
flt->rdatime=  0.00
flt->rdatend=104.00
atst->ldahisty=T
atst->ldaplot=T
atst->kdarate=1
#
atst->ldaprint=T
WAIT 1
flt->lfxfastm=T
flt->rfxpitcm=   2.32   # theta (deg)
flt->rfxrolcm=  -1.64   # phi (deg)
flt->rfxhdgcm= 291.34+   0.00   # hdg (deg)
#
WAIT 1
#
flt->dmd_cg_in=202.661     # Center of Gravity (in)
flt->fsetcg=T
flt->fsetgw=T
P '$AUTO/atg/trim_start.pag'
flt->ldatston=T
flt->frz=F
#
WAIT flt->ldatrimd T
atst->fsetstab=F
WAIT atst->ldadoprt F
#
#  TEST COMPLETE
#  -------------
#
WAIT atst->ldatstip F
WAIT (atst->kdaiocmd == 0)
#
p '$AUTO/atg/tst_end.pag'
ctl->fsetstabsw=F
x
