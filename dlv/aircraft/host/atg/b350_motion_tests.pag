********************************************************************************
* %OPLICENSE%                                                                  *
*             RESTRICTED PROPRIETARY SOFTWARE AND DATA                         *
*                                                                              *
* This software and the data incorporated herein is licensed, not sold,        *
* and is protected by Federal and State copyright and related intellectual     *
* property laws. It may not be disclosed, copied, reversed engineered or used  *
* in any manner except in strict accordance with the terms and conditions of   *
* a certain License granted by OPINICUS to FLYRIGHT HOLDINGS INC. and only on  *
* the one King Air B350/200 simulator to which it relates. Sale, lease or      *
* other transfer of the simulator does not authorize the transferee to use     *
* this software and the data incorporated herein unless strict compliance with *
* the terms of the License referenced above has been met. A copy of the        *
* License is available from OPINICUS upon request. Third party licensed        *
* proprietary data may also be incorporated herein and is subject to the       *
* terms of those licenses.                                                     *
* %OPLICENSE%                                                                  * 
********************************************************************************
###############################
### MOTION VIBRATIONS TESTS ###
###############################
 RUN f03601100000000  #  Motion Vibrations - Thrust Effect with Brakes Set          (based on f01204400303310, normal takeoff)
 RUN f03602100000000  #  Motion Vibrations - Buffet w/ Landing Gear Extended        (based on f02304302201410, gear change dynamics(extension) )
 RUN f03603100000000  #  Motion Vibrations - Buffet w/ Flaps Extended               (based on f02302400701410, flap change dynamics(extension) )
 RUN f03605100000000  #  Motion Vibrations - Buffet at Approach to Stall            (based on f02308302705310, Stall)
 RUN f03606100000000  #x Motion Vibrations - High Speed Mach Buffet                 (based on f02305101401610, longitudinal trim)
 RUN f03607100000000  #  Motion Vibrations - In Flight Vibration For Prop Airplanes (based on f02305101401610, longitudinal trim)

######################
### LATENCY TESTS ###
######################
  RUN f04101000000000  #  Transport Delay, Pitch
  RUN f04101000000000  #  Transport Delay, Roll
  RUN f04101000000000  #  Transport Delay, Yaw

#########################################
### MOTION SYSTEM REPEATABILITY TESTS ###
#########################################
 RUN f03401000000000  #  Motion System Repeatability - Surge In Air     (based on f01204400303310, normal takeoff)
 RUN f03402000000000  #  Motion System Repeatability - Sway In Air      (based on f01204400303310, normal takeoff)
 RUN f03403000000000  #  Motion System Repeatability - Heave In Air     (based on f01204400303310, normal takeoff)
 RUN f03404000000000  #  Motion System Repeatability - Roll In Air      (based on f01204400303310, normal takeoff)
 RUN f03405000000000  #  Motion System Repeatability - Pitch In Air     (based on f01204400303310, normal takeoff)
 RUN f03406000000000  #  Motion System Repeatability - Yaw In Air       (based on f01204400303310, normal takeoff)

 RUN f03401100000000  #  Motion System Repeatability - Surge On Ground  (based on f01204400303310, normal takeoff)
 RUN f03402100000000  #  Motion System Repeatability - Sway On Ground   (based on f01204400303310, normal takeoff)
 RUN f03403100000000  #  Motion System Repeatability - Heave On Ground  (based on f01204400303310, normal takeoff)
 RUN f03404100000000  #  Motion System Repeatability - Roll On Ground   (based on f01204400303310, normal takeoff)
 RUN f03405100000000  #  Motion System Repeatability - Pitch On Ground  (based on f01204400303310, normal takeoff)
 RUN f03406100000000  #  Motion System Repeatability - Yaw On Ground    (based on f01204400303310, normal takeoff)
                                                                                   
#######################################
### MOTION CUEING PERFORMANCE TESTS ###
#######################################
 RUN f03501000000000  #  Motion Cueing Performance - Takeoff Rotation (VR to V2)       (based on f01204400303310, normal takeoff)
 RUN f03502000000000  #x Motion Cueing Performance - Engine Failure Between V1 and VR  (based on f01205200106510, engine out takeoff)
 RUN f03503000000000  #  Motion Cueing Performance - Pitch Change During Go-Around     (based on f02507300508610, engine inoperative go-around, modified to use both engines)
 RUN f03504100000000  #  Motion Cueing Performance - Configuration Changes, Flaps      (based on f02302400701410, flap change dynamics(extension) )
 RUN f03504200000000  #  Motion Cueing Performance - Configuration Changes, Gear       (based on f02304302201410, gear change dynamics(extension) ) 
 RUN f03505000000000  #  Motion Cueing Performance - Power Change Dynamics             (based on f02301402001410, power change dynamics )
 RUN f03506000000000  #x Motion Cueing Performance - Landing Flare                     (based on f02501600708610, normal landing )
 RUN f03507000000000  #x Motion Cueing Performance - Touchdown Bump                    (based on f02501600708610, normal landing )

##################################################################################
### MOTION ATP TESTS (require interaction with motion PC GUI, see Rexroth ATP) ###
##################################################################################
# Motion Frequency Response         run motion_freq_resp script on host,    results are in f31011000000000.pdf
# Motion Leg Balance                run leg_balance_test script on host,    results are in f32011000000000.pdf
# Motion Turn Around Check          run leg_smoothness_test script on host, results are in f33011000000000.pdf
