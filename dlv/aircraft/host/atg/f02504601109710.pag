################################################################################
* %OPLICENSE%                                                                  *
*             RESTRICTED PROPRIETARY SOFTWARE AND DATA                         *
*                                                                              *
* This software and the data incorporated herein is licensed, not sold,        *
* and is protected by Federal and State copyright and related intellectual     *
* property laws. It may not be disclosed, copied, reversed engineered or used  *
* in any manner except in strict accordance with the terms and conditions of   *
* a certain License granted by OPINICUS to FLYRIGHT HOLDINGS INC. and only on  *
* the one King Air B350/200 simulator to which it relates. Sale, lease or      *
* other transfer of the simulator does not authorize the transferee to use     *
* this software and the data incorporated herein unless strict compliance with *
* the terms of the License referenced above has been met. A copy of the        *
* License is available from OPINICUS upon request. Third party licensed        *
* proprietary data may also be incorporated herein and is subject to the       *
* terms of those licenses.                                                     *
* %OPLICENSE%                                                                  * 
################################################################################
#
#  TITLE:       Engine Out Landing
#  -----
#
#  PROJECT:     BEECHCRAFT KING AIR 350
#  -------
#
#  AIRCRAFT:    B350
#  --------
#
#  CRITERIA:    2.e.4
#  --------
#
#  TEST NUMBER: f02504601109710
#  -----------
#
##############################################
#
#  PRELIMINARY
#  -----------
#
# FAA:               2.e.4
# Maneuver:          Engine Out Landing
# File No:           k4402011_cmb.csv
# Flight Test Date:  02/01/01
# Start FT_Time:     0.00
# Stop  FT_Time:     137.98
# Start OP_Time:     8.00
# Stop  OP_Time:     53.00
# -------------------------------------
# Flap:              35.00
# Gear:              1.00
# Weight:            11741.2
# Hp:                576.2
# Kcas:              114.91
# Mach:              0.1749
# TAT:               -12.965
#
WAIT 1
C atst->kdatstno 2504601109710
#
strcpy atst->project   'Beechcraft B350 Training Device - v.'
strcpy atst->aircraft  'B350'
strcpy atst->tst_title 'Engine Out Landing'
strcpy atst->faa_tstno '2.e.4.a'
strcpy atst->eng_type  'PT6A-60A'
strcpy atst->ft_file   'k4402011_cmb.csv'
#
atst->ft_time_hack[0]=8.0
atst->ft_time_hack[1]=53.0
#
#
WAIT 1
#
# Include Flight Test Trim Values
# -------------------------------
p '$AUTO/atg/ft_f02504601109710.pag'
#
WAIT RESET
WAIT DEFAULT 1000
AUTOCHANGE NO
WAIT 1
WAIT sim->config_inprogress F
flt->frz=T
# flt->fmwtcg=T
flt->nposfrz=T
flt->ftgactiv=T
atst->lixindis=T
#
#  PARAMETER PLOT
#  --------------
#
PLOT BEGIN
PLOT POINTS
PLOT TITLE 'Engine Out Landing'
PLOT FILE 'f02504601109710.PLT'
P '$AUTO/plot/f02504601109710.plt'
PLOT END
#
#  LFI TABLES
#  ----------
#
LFI RESET
P '$AUTO/lfi_atg/f02504601109710.lfi'
#
##############################################
# Reposition to Home Airport Location
P '$AUTO/atg/init_takeoff_repos.pag'
#
#  WEIGHT AND BALANCE
#  ------------------
#
# Total Fuel Load divided evenly between left and right
fuel->fdemtnk[0]= 714.6  # Left Total  = 685.4 (Main=685.4,Aux=  0.0)
fuel->fdemtnk[1]= 714.6  # Right Total = 743.8 (Main=743.8,Aux=  0.0)
fuel->fsetfuel=T
flt->fdemgw=     11741.2  # Gross Weight (lbs)
flt->fsetgw=T
wait 20 flt->fwgrss 11541.2 11941.2
flt->dmd_cg_in=197.585     # Center of Gravity (in)
flt->fsetcg=T
flt->fsetgw=T
flt->fdemixx=     20825.3  # Ixx (slug-ft^2)
flt->fdemiyy=     17916.3  # Iyy (slug-ft^2)
flt->fdemizz=     35392.1  # Izz (slug-ft^2)
flt->fdemixz=      1847.6  # Ixz (slug-ft^2)
# flt->fsetiner=T            # Set Inertias
#
#  ENVIRONMENT
#  -----------
#
flt->fdembaro=  29.92  # barometric pressure
WAIT 1
flt->fsetbaro=T
flt->fdemtemp= -14.40  # oat (deg C)
WAIT 1
flt->fsetoat=T
flt->fdemwdir=250.0 #+flt->magvar # 251.96+flt->magvar # 0.00  #    0.00 # Wind Direction
flt->fdemwsp=6.4 #5.5 #    5.50  #    0.00 # Wind Speed
flt->fsetwdir=T
flt->fsetwsp=T
flt->fdemrwc=0         # runway conditions, dry
flt->fsetrwc=T
#
#  FLIGHT CONDITIONS
#  -----------------
#
flt->fdemhter=0.0      # 0.0 field elevation (ft)
flt->fsethter=T
flt->fdemalt=  576.2 -3.0   # alt (ft)
flt->fsetalt=1
wait 20 flt->fhp   526.2   626.2
flt->fdemhter=339.8 #-4.5 #346.0-33.0-4.2 #-3.5      # 0.0 field elevation (ft)
flt->fsethter=T
atst->rzcalthp=  576.2  -3.0  # demanded alt (ft)
#
# Generic Runway Setup
vis->atg_sel_hdg=244.5
vis->atg_sel_lat_tdz=27.9572358480
vis->atg_sel_lon_tdz=-82.5111028961
P '$AUTO/atg/flanding.pag'
#
flt->rtcvc=    114.91   # vc (kts)
flt->rzcroc=   -524.59-25.0   # roc (fpm)
flt->rfxpitcm=   -1.34   # theta (deg)
flt->rfxrolcm=    1.24   # phi (deg)
flt->rfxhdgcm= 251.96+flt->magvar  # hdg (deg)
#
#  AIRCRAFT CONFIGURATION
#  ----------------------
#
ctl->fsetflap=T
ctl->fdemflap=   2.00  # flap (deg)
ctl->fsetgear=T
ctl->fdemgear=   1.00  # gear
wait 20 ctl->gear.avg_pos  0.90  1.10
wait 20 ctl->flap.avg_surf_pos 34.50 35.50 # (deg)
#
flt->fdemstab=    6.862   #   6.862 longitudinal trim pos.
atst->fdemrtab=   6.447   #   6.447 rudder trim pos.
atst->fdematab=   2.259   #   2.259 aileron trim pos.
atst->fsetstab=T
atst->fsetrtab=T
atst->fsetatab=T
atst->fsetydmp=F  # Yaw Damper
#
#  ENGINES
#  -------
#
flt->rzcn1[0]=      72.930  # Engine 1 N1 (perc)
flt->rzcn1[1]=      87.046  # Engine 2 N1 (perc)
atst->rzcn2[0]=   1489.847  # Engine 1 N2 (perc)
atst->rzcn2[1]=   1684.661  # Engine 2 N2 (perc)
eng->dmd_prla[0]=   40.14   #    39.43 Engine 1 PROPELLER LEVER ANGLE (deg)
eng->dmd_prla[1]=   40.14   #    40.86 Engine 2 PROPELLER LEVER ANGLE (deg)
eng->dmd_cl[0]=     20.13   #    19.83 Engine 1 CONDITION LEVER ANGLE (deg)
eng->dmd_cl[1]=     20.13   #    20.43 Engine 2 CONDITION LEVER ANGLE (deg)
eng->fdemeng[0]=    39.50   # Engine 1 TLA (deg)
eng->fdemeng[1]=    49.97   # Engine 2 TLA (deg)
atst->rzctq[0]=    162.94   # Engine 1 Torque (ft-lb)
atst->rzctq[1]=   1595.22   # Engine 2 Torque (ft-lb)
atst->tla_offset[0] = -8.0
eng->ebstart[:]=1           # Faststart both engines
wait 1
#
#  SYSTEMS
#  -------
#
hyd.In.di_park_brake=F
atst->fdemtoeb=   0.12  # Cmd Brake Pos
atst->fdemtoer=   0.23  # Cmd Brake Pos
atst->fsettoeb=T
atst->fsettoer=T
atst->rzcbp[0]=   1.98  # Cmd Brk Press
atst->rzcbp[1]=   0.00  # Cmd Brk Press
#
##############################################
#
#  TRIMMER
#  -------
#
atst->kaxpmode=4        # PRIMARY CONTROLS PITCH MODE
atst->kaxrmode=4        # PRIMARY CONTROLS ROLL MODE
atst->kaxymode=4        # PRIMARY CONTROLS YAW MODE
atst->rdtrtime=30.00    # TIME WAIT ELAPSED
atst->ldatrmon(1)=T     # TRIM TIMER
atst->ldatrmon(2)=F     #
atst->ldatrmon(3)=F     #
atst->ldatrmon(4)=T     # ROC
atst->ldatrmon(5)=F     #
atst->ldatrmon(6)=T     # TRANSLATIONAL ACCEL
atst->ldatrmon(7)=T     # ROTATIONAL ACCEL
atst->ldatrmon(8)=F     # ROTATIONAL RATES, 0 IF COMMANDING BANK ANGLE
atst->ldatrmon(9)=F     # LONGITUDINAL CONTROL FORCE
atst->ldatrmon(10)=F    #
atst->ldatrmon(11)=F    #
atst->ldatrmon(12)=T    # GROSS WEIGHT
atst->ldatrmon(13)=T    # x CG
atst->ldatrmon(14)=F    #
atst->ldatrmon(15)=F    #
flt->ifxtrim=3          # TRIM TYPE
atst->rzpgain[0]=0.15
atst->rzpgain[9] = 0.05
atst->rzigain[9] = 1.0
#
flt->rzcudot=   0.000   # udot (ft/sec**2)     0.218
flt->rzcvdot=  -1.600   # vdot (ft/sec**2)    -0.312
flt->rzcwdot=   0.000   # wdot (ft/sec**2)   -31.954
flt->rzcpdot=   0.000   # pdot (rad/sec**2)    0.000
flt->rzcqdot=   0.000   # qdot (rad/sec**2)    0.000
flt->rzcrdot=   0.000   # rdot (rad/sec**2)    0.000
flt->rzcpds =   0.000   # p (deg/sec)          0.370
flt->rzcqds =   0.000   # q (deg/sec)         -0.375
flt->rzcrds =   0.000   # r (deg/sec)          0.333
#
atst->lzsslip=F         # STEADY SIDSLIP TRIM
flt->lfxstabt=F
atst->lfxailt=F
atst->lfxrudt=F
#
#  DRIVER
#  ------
#
#
atst->lzaoahld=F        #  AP AOA HOLD FLAG
atst->lzaoacf=F         #  AOA HOLD WITH CL SHIFT
atst->lzpithld=T        #  AP PITCH HOLD FLAG
atst->lzpitalthld=T     #  AP PITCH HOLD WITH ALTITUDE BIAS FLAG
atst->lzpitiashld=F     #  AP PITCH HOLD WITH IAS BIAS FLAG
atst->lzpitrochld=F     #  AP PITCH HOLD WITH ROC BIAS FLAG
atst->lzpassist=F       #  PITCH DRIVE ASSIST FLAG
atst->lzpitcf=F         #  PITCH HOLD WITH CM SHIFT
atst->lzcradalt=F       #  CMD ALT IS RAD ALT
atst->lzrochld=F        #  AP ROC HOLD FLAG
atst->lzrolhld=T        #  AP ROLL HOLD FLAG
atst->lzrassist=F       #  ROLL DRIVE ASSIST FLAG
atst->lzrolcf=F         #  ROLL HOLD WITH CR SHIFT
atst->lzbethld=F        #  AP BETA HOLD FLAG
atst->lzyassist=T       #  YAW DRIVE ASSIST FLAG
atst->lzbetcf=F         #  BETA HOLD WITH YAW MOMENT
atst->lzhdghld=T        #  AP HDG HOLD WITH RUD FLAG
atst->lzhdgcf=F         #  HEADING HOLD WITH CN SHIFT
atst->lzrwyhld=F        #  RUNWAY HEADING HOLD
atst->lzrwycf=F         #  RUNWAY HOLD WITH YAW MOMENT
atst->lzgamhld=F        #  GAMMA HOLD
atst->lznycf=F          #  NY HOLD WITH YAW MOMENT
atst->lziashld=F        #  AP IAS HOLD FLAG
atst->lziasphd=F        #  IAS HOLD VIA PITCH A/P FLAG
atst->lziascf=F         #  IAS HOLD WITH CD SHIFT
atst->lzmachld=F        #  AP MACH HOLD FLAG
atst->lzfnhld=F         #  AP FN HOLD FLAG
atst->lzfnudothld=F     #  FN HLD WITH UDOT BIAS
atst->lzfniashld=F      #  FN HLD WITH IAS BIAS
atst->lzn1hld=F         #  AP N1 HOLD FLAG
flt->lztqhld=F          #  TORQUE HOLD
atst->lztqudothld=F     #  TORQUE HOLD WITH UDOT BIAS
atst->lztassist=F       #  TORQUE HOLD THORTTLE ASSIST
atst->lzeprhld=F        #  AP EPR HOLD FLAG
atst->lzreverse(1)=F    #  REVERSE THRUST FLAG
atst->lzreverse(2)=F    #  REVERSE THRUST FLAG
atst->lzbrknx=F         #  NX BRAKE HOLD
atst->lzbphld=F         #  BRAKE PRESS HOLD WITH PEDAL
atst->lzflare=T         #  FLARE MODE
atst->rzflareh=10.0
#
atst->lziascf=F         #  IAS HOLD WITH CD SHIFT
atst->lzaoacf=F         #  AOA HOLD WITH CL SHIFT
atst->lzpitcf=F         #  PITCH HOLD WITH CM SHIFT
#
atst.rzpgain[6]=0.06
#
#  BEGIN TEST
#  ----------
#
flt->rdatime=  0.00
flt->rdatend= 45.00
atst->ldahisty=T
atst->ldaplot=T
atst->kdarate=1
#
atst->ldaprint=T
WAIT 1
flt->lfxfastm=T
flt->rfxpitcm=  -1.34   # theta (deg)
flt->rfxrolcm=   1.24   # phi (deg)
flt->rfxhdgcm= 251.96+flt->magvar  # hdg (deg)
#
WAIT 1
#
flt->dmd_cg_in=197.585     # Center of Gravity (in)
flt->fsetcg=T
flt->fsetgw=T
P '$AUTO/atg/trim_start.pag'
atst->rdatrmlm(6)=0.1
atst->rztqpgain[:] = 0.030
atst->rztqdgain[:]=0.8
flt->ldatston=T
flt->frz=F
#
WAIT flt->ldatrimd T
WAIT atst->ldadoprt F
#
#  TEST COMPLETE
#  -------------
#
WAIT atst->ldatstip F
WAIT (atst->kdaiocmd == 0)
#
p '$AUTO/atg/tst_end.pag'
x
