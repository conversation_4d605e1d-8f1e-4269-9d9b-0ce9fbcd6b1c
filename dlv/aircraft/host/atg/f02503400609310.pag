################################################################################
* %OPLICENSE%                                                                  *
*             RESTRICTED PROPRIETARY SOFTWARE AND DATA                         *
*                                                                              *
* This software and the data incorporated herein is licensed, not sold,        *
* and is protected by Federal and State copyright and related intellectual     *
* property laws. It may not be disclosed, copied, reversed engineered or used  *
* in any manner except in strict accordance with the terms and conditions of   *
* a certain License granted by OPINICUS to FLYRIGHT HOLDINGS INC. and only on  *
* the one King Air B350/200 simulator to which it relates. Sale, lease or      *
* other transfer of the simulator does not authorize the transferee to use     *
* this software and the data incorporated herein unless strict compliance with *
* the terms of the License referenced above has been met. A copy of the        *
* License is available from OPINICUS upon request. Third party licensed        *
* proprietary data may also be incorporated herein and is subject to the       *
* terms of those licenses.                                                     *
* %OPLICENSE%                                                                  * 
################################################################################
#
#  TITLE:       Crosswind Landing
#  -----
#
#  PROJECT:     BEECHCRAFT KING AIR 350
#  -------
#
#  AIRCRAFT:    B350
#  --------
#
#  CRITERIA:    2.e.3
#  --------
#
#  TEST NUMBER: f02503400609310
#  -----------
#
##############################################
#
#  PRELIMINARY
#  -----------
#
# FAA:               2.e.3
# Maneuver:          Crosswind Landing
# File No:           k4204006_cmb.csv
# Flight Test Date:  01/29/01
# Start FT_Time:     0.00
# Stop  FT_Time:     76.98
# Start OP_Time:     22.00
# Stop  OP_Time:     67.00
# -------------------------------------
# Flap:              14.00
# Gear:              1.00
# Weight:            11353.4
# Hp:                556.8
# Kcas:              116.18
# Mach:              0.1767
# TAT:               -7.281
#
WAIT 1
C atst->kdatstno 2503400609310
#
strcpy atst->project   'Beechcraft B350 Training Device - v.'
strcpy atst->aircraft  'B350'
strcpy atst->tst_title 'Crosswind Landing'
strcpy atst->faa_tstno '2.e.3.a'
strcpy atst->eng_type  'PT6A-60A'
strcpy atst->ft_file   'k4204006_cmb.csv'
#
atst->ft_time_hack[0]=22.0
atst->ft_time_hack[1]=67.0
#
#
WAIT 1
#
# Include Flight Test Trim Values
# -------------------------------
p '$AUTO/atg/ft_f02503400609310.pag'
#
WAIT RESET
WAIT DEFAULT 1000
AUTOCHANGE NO
WAIT 1
WAIT sim->config_inprogress F
flt->frz=T
# flt->fmwtcg=T
flt->nposfrz=T
flt->ftgactiv=T
atst->lixindis=T
#
#  PARAMETER PLOT
#  --------------
#
PLOT BEGIN
PLOT POINTS
PLOT TITLE 'Crosswind Landing'
PLOT FILE 'f02503400609310.PLT'
P '$AUTO/plot/f02503400609310.plt'
PLOT END
#
#  LFI TABLES
#  ----------
#
LFI RESET
P '$AUTO/lfi_atg/f02503400609310.lfi'
#
##############################################
# Reposition to Home Airport Location
P '$AUTO/atg/init_takeoff_repos.pag'
#
#  WEIGHT AND BALANCE
#  ------------------
#
# Total Fuel Load divided evenly between left and right
fuel->fdemtnk[0]= 520.7  # Left Total  = 548.8 (Main=548.8,Aux=  0.0)
fuel->fdemtnk[1]= 520.7  # Right Total = 492.6 (Main=492.6,Aux=  0.0)
fuel->fsetfuel=T
flt->fdemgw=     11353.4  # Gross Weight (lbs)
flt->fsetgw=T
wait 20 flt->fwgrss 11153.4 11553.4
flt->dmd_cg_in=197.420     # Center of Gravity (in)
flt->fsetcg=T
flt->fsetgw=T
flt->fdemixx=     18803.9  # Ixx (slug-ft^2)
flt->fdemiyy=     17909.5  # Iyy (slug-ft^2)
flt->fdemizz=     33360.6  # Izz (slug-ft^2)
flt->fdemixz=      1849.0  # Ixz (slug-ft^2)
# flt->fsetiner=T            # Set Inertias
#
#  ENVIRONMENT
#  -----------
#
flt->fdembaro=  29.92  # barometric pressure
WAIT 1
flt->fsetbaro=T
flt->fdemtemp=  -8.78  # oat (deg C)
WAIT 1
flt->fsetoat=T
#flt->fdemwdir= 247.71+flt->magvar #   247.71 + flt->magvar #    0.00 # Wind Direction
#flt->fdemwsp= 4.75  #     12.0 #  0.00 # Wind Speed
flt->fdemwdir=230.0 #261.0 #-205.6+180.0 #314.59 #+flt->magvar
flt->fdemwsp=8.0 #18.9 #4.75 #6.54 #2.68
flt->fsetwdir=T
flt->fsetwsp=T
flt->fdemrwc=0         # runway conditions, dry
flt->fsetrwc=T
#
#  FLIGHT CONDITIONS
#  -----------------
#
flt->fdemhter=0.0      # 0.0 field elevation (ft)
flt->fsethter=T
flt->fdemalt=  556.8    # alt (ft)
flt->fsetalt=1
wait 20 flt->fhp   506.8   606.8
flt->fdemhter=278.0 #291.0-8.0      # 0.0 field elevation (ft)
flt->fsethter=T
atst->rzcalthp=  556.8   # demanded alt (ft)
#
# Generic Runway Setup
vis->atg_sel_hdg=313.0
vis->atg_sel_lat_tdz=27.9722407210
vis->atg_sel_lon_tdz=-82.5084785502
P '$AUTO/atg/flanding.pag'
#
flt->rtcvc=    116.18   # vc (kts)
flt->rzcroc=   -341.37-84.0   # roc (fpm)
flt->rfxpitcm=    2.18   # theta (deg)
flt->rfxrolcm=   -0.09   # phi (deg)
flt->rfxhdgcm= 314.59 + flt->magvar   # hdg (deg)
#
#  AIRCRAFT CONFIGURATION
#  ----------------------
#
ctl->fsetflap=T
ctl->fdemflap=   1.00  # flap (deg)
ctl->fsetgear=T
ctl->fdemgear=   1.00  # gear
wait 20 ctl->gear.avg_pos  0.90  1.10
wait 20 ctl->flap.avg_surf_pos 13.50 14.50 # (deg)
#
flt->fdemstab=    6.332   #   6.332 longitudinal trim pos.
atst->fdemrtab=  -1.909   #  -1.909 rudder trim pos.
atst->fdematab=  -0.133   #  -0.133 aileron trim pos.
atst->fsetstab=T
atst->fsetrtab=T
atst->fsetatab=T
atst->fsetydmp=F  # Yaw Damper
#
#  ENGINES
#  -------
#
flt->rzcn1[0]=      80.166  # Engine 1 N1 (perc)
flt->rzcn1[1]=      80.994  # Engine 2 N1 (perc)
atst->rzcn2[0]=   1627.594  # Engine 1 N2 (perc)
atst->rzcn2[1]=   1642.527  # Engine 2 N2 (perc)
eng->dmd_prla[0]=   39.71   #    39.41 Engine 1 PROPELLER LEVER ANGLE (deg)
eng->dmd_prla[1]=   39.71   #    40.01 Engine 2 PROPELLER LEVER ANGLE (deg)
eng->dmd_cl[0]=     20.04   #    19.63 Engine 1 CONDITION LEVER ANGLE (deg)
eng->dmd_cl[1]=     20.04   #    20.44 Engine 2 CONDITION LEVER ANGLE (deg)
eng->fdemeng[0]=    46.66   #    45.53 Engine 1 TLA (deg)
eng->fdemeng[1]=    46.66   #    47.78 Engine 2 TLA (deg)
atst->rzctq[0]=    509.61   # Engine 1 Torque (ft-lb)
atst->rzctq[1]=    570.50   # Engine 2 Torque (ft-lb)
eng->ebstart[:]=1           # Faststart both engines
wait 1
#
#  SYSTEMS
#  -------
#
hyd.In.di_park_brake=F
atst->fdemtoeb=   0.53 #0.17  # Cmd Brake Pos
atst->fdemtoer=   0.53  # Cmd Brake Pos
atst->fsettoeb=T
atst->fsettoer=T
atst->rzcbp[0]=   0.00  # Cmd Brk Press
atst->rzcbp[1]=   0.00  # Cmd Brk Press
#
##############################################
#
#  TRIMMER
#  -------
#
atst->kaxpmode=4        # PRIMARY CONTROLS PITCH MODE
atst->kaxrmode=4        # PRIMARY CONTROLS ROLL MODE
atst->kaxymode=4        # PRIMARY CONTROLS YAW MODE
atst->rdtrtime=30.00    # TIME WAIT ELAPSED
atst->ldatrmon(1)=T     # TRIM TIMER
atst->ldatrmon(2)=F     #
atst->ldatrmon(3)=F     #
atst->ldatrmon(4)=T     # ROC
atst->ldatrmon(5)=F     #
atst->ldatrmon(6)=T     # TRANSLATIONAL ACCEL
atst->ldatrmon(7)=T     # ROTATIONAL ACCEL
atst->ldatrmon(8)=F     # ROTATIONAL RATES, 0 IF COMMANDING BANK ANGLE
atst->ldatrmon(9)=F     # LONGITUDINAL CONTROL FORCE
atst->ldatrmon(10)=F    #
atst->ldatrmon(11)=F    #
atst->ldatrmon(12)=T    # GROSS WEIGHT
atst->ldatrmon(13)=T    # x CG
atst->ldatrmon(14)=F    #
atst->ldatrmon(15)=F    #
flt->ifxtrim=3          # TRIM TYPE
atst->rzdgain[8]=0.5
atst->rzpitaltk=0.2
atst->rzcpitaltlim=10.0
atst->rzcpitiaslim=10.0
atst->rzpgain[0]=0.15
atst->rzpgain[9] = 0.05
#
flt->rzcudot=  -0.880+0.4+0.2   # udot (ft/sec**2)    -0.499
flt->rzcvdot= 1.415 #  0.150   # vdot (ft/sec**2)     0.391
flt->rzcwdot=   0.000   # wdot (ft/sec**2)   -31.811
flt->rzcpdot=   0.000   # pdot (rad/sec**2)    0.000
flt->rzcqdot=   0.000   # qdot (rad/sec**2)    0.000
flt->rzcrdot=   0.000   # rdot (rad/sec**2)    0.000
flt->rzcpds =   0.000   # p (deg/sec)          0.872
flt->rzcqds =   0.000   # q (deg/sec)         -0.386
flt->rzcrds =  -0.080   # r (deg/sec)         -0.326
#
atst->lzsslip=F         # STEADY SIDSLIP TRIM
flt->lfxstabt=F
atst->lfxailt=F
atst->lfxrudt=F
#
#  DRIVER
#  ------
#
#
atst->lzaoahld=F        #  AP AOA HOLD FLAG
atst->lzaoacf=F         #  AOA HOLD WITH CL SHIFT
atst->lzpithld=T        #  AP PITCH HOLD FLAG
atst->lzpitalthld=F     #  AP PITCH HOLD WITH ALTITUDE BIAS FLAG
atst->lzpitiashld=F     #  AP PITCH HOLD WITH IAS BIAS FLAG
atst->lzpitrochld=F     #  AP PITCH HOLD WITH ROC BIAS FLAG
atst->lzpassist=F       #  PITCH DRIVE ASSIST FLAG
atst->lzpitcf=F         #  PITCH HOLD WITH CM SHIFT
atst->lzcradalt=F       #  CMD ALT IS RAD ALT
atst->lzrochld=F        #  AP ROC HOLD FLAG
atst->lzrolhld=T        #  AP ROLL HOLD FLAG
atst->lzrassist=T       #  ROLL DRIVE ASSIST FLAG
atst->lzrolcf=F         #  ROLL HOLD WITH CR SHIFT
atst->lzbethld=F        #  AP BETA HOLD FLAG
atst->lzyassist=F       #  YAW DRIVE ASSIST FLAG
atst->lzbetcf=F         #  BETA HOLD WITH YAW MOMENT
atst->lzhdghld=T        #  AP HDG HOLD WITH RUD FLAG
atst->lzhdgcf=F         #  HEADING HOLD WITH CN SHIFT
atst->lzrwyhld=F        #  RUNWAY HEADING HOLD
atst->lzrwycf=F         #  RUNWAY HOLD WITH YAW MOMENT
atst->lzgamhld=F        #  GAMMA HOLD
atst->lznycf=F          #  NY HOLD WITH YAW MOMENT
atst->lziashld=F        #  AP IAS HOLD FLAG
atst->lziasphd=F        #  IAS HOLD VIA PITCH A/P FLAG
atst->lziascf=F         #  IAS HOLD WITH CD SHIFT
atst->lzmachld=F        #  AP MACH HOLD FLAG
atst->lzfnhld=F         #  AP FN HOLD FLAG
atst->lzfnudothld=F     #  FN HLD WITH UDOT BIAS
atst->lzfniashld=F      #  FN HLD WITH IAS BIAS
atst->lzn1hld=F         #  AP N1 HOLD FLAG
flt->lztqhld=F          #  TORQUE HOLD
atst->lztqudothld=F     #  TORQUE HOLD WITH UDOT BIAS
atst->lztassist=F       #  TORQUE HOLD THORTTLE ASSIST
atst->lzeprhld=F        #  AP EPR HOLD FLAG
atst->lzreverse(1)=F    #  REVERSE THRUST FLAG
atst->lzreverse(2)=F    #  REVERSE THRUST FLAG
atst->lzbrknx=F         #  NX BRAKE HOLD
atst->lzbphld=F         #  BRAKE PRESS HOLD WITH PEDAL
atst->lzflare=T         #  FLARE MODE
#atst->rzflareh=35.0
#atst->rzflareh=15.0
atst->rzflareh=120.0
flt->frwyhdg=319.2
#
#atst->lziasct=1
atst->rzigct=1.5
atst->rzpgct=2.0
#
atst->lzpitcf=F         #  PITCH HOLD WITH CM SHIFT
atst->lzaoacf=F         #  AOA HOLD WITH CL SHIFT
atst->lziascf=F         #  IAS HOLD WITH CD SHIFT
#
#  BEGIN TEST
#  ----------
#
flt->rdatime=  0.00
flt->rdatend= 45.00
atst->ldahisty=T
atst->ldaplot=T
atst->kdarate=1
#
atst->ldaprint=T
WAIT 1
flt->lfxfastm=T
flt->rfxpitcm=   2.18   # theta (deg)
flt->rfxrolcm=  -0.09   # phi (deg)
flt->rfxhdgcm= 314.59 + flt->magvar   # hdg (deg)
#
WAIT 1
#
flt->dmd_cg_in=197.420     # Center of Gravity (in)
flt->fsetcg=T
flt->fsetgw=T
P '$AUTO/atg/trim_start.pag'
atst->rdatrmlm(6)=0.1
# Engine Anti-Ice OFF
sio->LXDI010705=T
sio->LXDI010704=T
flt->ldatston=T
flt->ldatston=T
flt->frz=F
#
WAIT flt->ldatrimd T
WAIT atst->ldadoprt F
#
#  TEST COMPLETE
#  -------------
#
WAIT atst->ldatstip F
WAIT (atst->kdaiocmd == 0)
#
p '$AUTO/atg/tst_end.pag'
x
