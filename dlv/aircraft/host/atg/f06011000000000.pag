
################################################################################
* %OPLICENSE%                                                                  *
*             RESTRICTED PROPRIETARY SOFTWARE AND DATA                         *
*                                                                              *
* This software and the data incorporated herein is licensed, not sold,        *
* and is protected by Federal and State copyright and related intellectual     *
* property laws. It may not be disclosed, copied, reversed engineered or used  *
* in any manner except in strict accordance with the terms and conditions of   *
* a certain License granted by OPINICUS to FLYRIGHT HOLDINGS INC. and only on  *
* the one King Air B350/200 simulator to which it relates. Sale, lease or      *
* other transfer of the simulator does not authorize the transferee to use     *
* this software and the data incorporated herein unless strict compliance with *
* the terms of the License referenced above has been met. A copy of the        *
* License is available from OPINICUS upon request. Third party licensed        *
* proprietary data may also be incorporated herein and is subject to the       *
* terms of those licenses.                                                     *
* %OPLICENSE%                                                                  * 
################################################################################
#
#  TITLE:      Attitude Test Pattern -- Automatic 
#  -----
C atst->kdatstno 6011000000000
#
strcpy atst->project   'Beechcraft B350 Training Device - v.'
strcpy atst->aircraft  'B350'
strcpy atst->tst_title 'Attitude'
strcpy atst->faa_tstno '4.j.4.a'
strcpy atst->eng_type  'PT6A-60A'
strcpy atst->ft_file   'N/A'
#
flt->frz=T
wait 2
c vis->io.test_pattern 13
c flt.ftgactiv 1
#
geo->init_lat=0.0
geo->init_lon=0.0
geo->init_quat=1
wait 5
wait 60 vis->dbloaded eq 1
flt->fhggnd=200.0
flt->fhp=200.0
flt->fuea=200.0
def IW iter
######### Roll
iter=0
flt->rfxpitcm=   0.00   # theta (deg)
flt->rfxrolcm=   0.00   # phi (deg)
flt->rfxhdgcm=   0.00   # hdg (deg)
fi->In.ios_inst_align=1
#
while(iter<=30 && flt->ftgactiv) {
  if(iter==0) {
     wait 5
  }
  flt->rfxrolcm=iter
  iter+=1
  fi->In.ios_inst_align=1
  wait 0.1
}
iter=30
while(iter>=0 && flt->ftgactiv) {
  if(iter==30) {
     wait 5
  }
  flt->rfxrolcm=iter
  iter-=1
  fi->In.ios_inst_align=1
  wait 0.1
}
#
iter=0
while(iter<=30 && flt->ftgactiv) {
  flt->rfxrolcm=-iter
  iter+=1
  fi->In.ios_inst_align=1
  wait 0.1
}
iter=30
while(iter>=0 && flt->ftgactiv) {
  if(iter==30) {
     wait 5
  }
  flt->rfxrolcm=-iter
  iter-=1
  fi->In.ios_inst_align=1
  wait 0.1
}
######### Pitch
iter=0
flt->rfxpitcm=   0.00   # theta (deg)
flt->rfxrolcm=   0.00   # phi (deg)
flt->rfxhdgcm=   0.00   # hdg (deg)
fi->In.ios_inst_align=1
#
while(iter<=15 && flt->ftgactiv) {
  flt->rfxpitcm=iter
  iter+=1
  fi->In.ios_inst_align=1
  wait 0.1
}
iter=15
while(iter>=0 && flt->ftgactiv) {
  if(iter==15) {
     wait 5
  }
  flt->rfxpitcm=iter
  iter-=1
  fi->In.ios_inst_align=1
  wait 0.1
}
#
iter=0
while(iter<=15 && flt->ftgactiv) {
  flt->rfxpitcm=-iter
  iter+=1
  fi->In.ios_inst_align=1
  wait 0.1
}
iter=15
while(iter>=0 && flt->ftgactiv) {
  if(iter==15) {
     wait 5
  }
  flt->rfxpitcm=-iter
  iter-=1
  fi->In.ios_inst_align=1
  wait 0.1
}
if(flt->ftgactiv) {
   wait 5
}
#geo->init_lat=37.613531
#geo->init_lon=-122.357139
#geo->init_quat=1
#flt->rfxhdgcm=297.890015
vis->io.to_repos=1
wait 5
#C flt->fipfld 1
C atst->kdatstno 0
p '$SIMH/page/abort.pag'
x
