################################################################################
* %OPLICENSE%                                                                  *
*             RESTRICTED PROPRIETARY SOFTWARE AND DATA                         *
*                                                                              *
* This software and the data incorporated herein is licensed, not sold,        *
* and is protected by Federal and State copyright and related intellectual     *
* property laws. It may not be disclosed, copied, reversed engineered or used  *
* in any manner except in strict accordance with the terms and conditions of   *
* a certain License granted by OPINICUS to FLYRIGHT HOLDINGS INC. and only on  *
* the one King Air B350/200 simulator to which it relates. Sale, lease or      *
* other transfer of the simulator does not authorize the transferee to use     *
* this software and the data incorporated herein unless strict compliance with *
* the terms of the License referenced above has been met. A copy of the        *
* License is available from OPINICUS upon request. Third party licensed        *
* proprietary data may also be incorporated herein and is subject to the       *
* terms of those licenses.                                                     *
* %OPLICENSE%                                                                  * 
################################################################################
#
#  TITLE:       Motion Cueing Performance - Pitch Change During Go-Around
#  -----
#
#  PROJECT:     BEECHCRAFT KING AIR 350
#  -------
#
#  AIRCRAFT:    B350
#  --------
#
#  CRITERIA:    3.e.3
#  --------
#
#  TEST NUMBER: f03503000000000 (based on f02507300508610)
#  -----------
#
##############################################
#
#  PRELIMINARY
#  -----------
#
# FAA:               3.e.3
# Maneuver:          Engine Out Go Around
# File No:           k4001005_cmb.csv
# Flight Test Date:  01/29/01
# Start FT_Time:     0.00
# Stop  FT_Time:     240.98
# Start OP_Time:     140.00
# Stop  OP_Time:     220.00
# -------------------------------------
# Flap:              35.00
# Gear:              1.00
# Weight:            14732.3
# Hp:                733.7
# Kcas:              116.92
# Mach:              0.1778
# TAT:               -12.108
#
WAIT 1
C atst->kdatstno 3503000000000
#
strcpy atst->project   'Beechcraft B350 Training Device - v.'
strcpy atst->aircraft  'B350'
strcpy atst->tst_title 'Motion Cueing - Pitch Change on Go-Around'
strcpy atst->faa_tstno '3.e.3.a'
strcpy atst->eng_type  'PT6A-60A'
strcpy atst->ft_file   'k4001005_cmb.csv'
#
atst->ft_time_hack[0]=140.0
atst->ft_time_hack[1]=220.0
#
#
WAIT 1
#
# Include Flight Test Trim Values
# -------------------------------
p '$AUTO/atg/ft_f03503000000000.pag'
#
WAIT RESET
WAIT DEFAULT 1000
AUTOCHANGE NO
WAIT 1
WAIT sim->config_inprogress F
flt->frz=T
# flt->fmwtcg=T
flt->nposfrz=T
flt->ftgactiv=T
atst->lixindis=T
#
#  PARAMETER PLOT
#  --------------
#
PLOT BEGIN
PLOT POINTS
PLOT TITLE 'Motion Cueing - Pitch Change on Go-Around'
PLOT FILE 'f03503000000000.PLT'
P '$AUTO/plot/f03503000000000.plt'
PLOT END
#
#  LFI TABLES
#  ----------
#
LFI RESET
P '$AUTO/lfi_atg/f03503000000000.lfi'
#
##############################################
#
#  WEIGHT AND BALANCE
#  ------------------
#
# Total Fuel Load divided evenly between left and right
fuel->fdemtnk[0]= 1660.2  # Left Total  = 1645.7 (Main=1239.7,Aux=406.1)
fuel->fdemtnk[1]= 1660.2  # Right Total = 1674.6 (Main=1268.5,Aux=406.1)
fuel->fsetfuel=T
flt->fdemgw=     14732.3  # Gross Weight (lbs)
flt->fsetgw=T
wait 20 flt->fwgrss 14532.3 14932.3
flt->dmd_cg_in=203.221     # Center of Gravity (in)
flt->fsetcg=T
flt->fsetgw=T
flt->fdemixx=     31889.5  # Ixx (slug-ft^2)
flt->fdemiyy=     20916.6  # Iyy (slug-ft^2)
flt->fdemizz=     49394.8  # Izz (slug-ft^2)
flt->fdemixz=      1791.1  # Ixz (slug-ft^2)
# flt->fsetiner=T            # Set Inertias
#
#  ENVIRONMENT
#  -----------
#
flt->fdembaro=  29.92  # barometric pressure
WAIT 1
flt->fsetbaro=T
flt->fdemtemp= -13.60  # oat (deg C)
WAIT 1
flt->fsetoat=T
flt->fdemwdir=   0.00  #    0.00 # Wind Direction
flt->fdemwsp=    0.00  #    0.00 # Wind Speed
flt->fsetwdir=T
flt->fsetwsp=T
flt->fdemrwc=0         # runway conditions, dry
flt->fsetrwc=T
#
#  FLIGHT CONDITIONS
#  -----------------
#
flt->fdemhter=0.0      # 0.0 field elevation (ft)
flt->fsethter=T
flt->fdemalt=  733.7    # alt (ft)
flt->fsetalt=1
wait 20 flt->fhp   683.7   783.7
flt->fdemhter=0.0      # 0.0 field elevation (ft)
flt->fsethter=T
atst->rzcalthp=  733.7   # demanded alt (ft)
#
flt->rtcvc=116.9 #117.5 #    116.92   # vc (kts)
flt->rzcroc=-645.0 #   -727.25+220 #+100.0   # roc (fpm)
flt->rfxpitcm=   -0.26   # theta (deg)
flt->rfxrolcm=    0.66   # phi (deg)
flt->rfxhdgcm=  75.23+flt->magvar #   0.00   # hdg (deg)
#
#  AIRCRAFT CONFIGURATION
#  ----------------------
#
ctl->fsetflap=T
ctl->fdemflap=   2.00  # flap (deg)
ctl->fsetgear=T
ctl->fdemgear=   1.00  # gear
wait 20 ctl->gear.avg_pos  0.90  1.10
wait 20 ctl->flap.avg_surf_pos 34.50 35.50 # (deg)
#
flt->fdemstab=    5.307   #   5.307 longitudinal trim pos.
atst->fdemrtab=   2.104   #   2.104 rudder trim pos.
atst->fdematab=   1.183   #   1.183 aileron trim pos.
atst->fsetstab=T
atst->fsetrtab=T
atst->fsetatab=T
atst->fsetydmp=F  # Yaw Damper
#
#  ENGINES
#  -------
#
flt->rzcn1[0]=      75.163  # Engine 1 N1 (perc)
flt->rzcn1[1]=      81.263  # Engine 2 N1 (perc)
atst->rzcn2[0]=   1543.944  # Engine 1 N2 (perc)
atst->rzcn2[1]=   1668.303  # Engine 2 N2 (perc)
eng->dmd_prla[0]=   20.0 #39.66   #    39.40 Engine 1 PROPELLER LEVER ANGLE (deg)
eng->dmd_prla[1]=   39.66   #    39.92 Engine 2 PROPELLER LEVER ANGLE (deg)
eng->dmd_cl[0]=     19.99   #    19.71 Engine 1 CONDITION LEVER ANGLE (deg)
eng->dmd_cl[1]=     19.99   #    20.27 Engine 2 CONDITION LEVER ANGLE (deg)
eng->fdemeng[0]=    41.18   # Engine 1 TLA (deg)
eng->fdemeng[1]=    46.92   # Engine 2 TLA (deg)
atst->rzctq[0]=    227.40   # Engine 1 Torque (ft-lb)
atst->rzctq[1]=    616.92   # Engine 2 Torque (ft-lb)
atst->tla_offset[0]=0.0
atst->tla_offset[1]=4.0
eng->ebstart[:]=1           # Faststart both engines
wait 1
#
#  SYSTEMS
#  -------
#
hyd.In.di_park_brake=F
atst->fdemtoeb=   0.00  # Cmd Brake Pos
atst->fdemtoer=   0.62  # Cmd Brake Pos
atst->fsettoeb=T
atst->fsettoer=T
atst->rzcbp[0]=   0.00  # Cmd Brk Press
atst->rzcbp[1]=   0.00  # Cmd Brk Press
#
##############################################
#
#  TRIMMER
#  -------
#
atst->kaxpmode=4        # PRIMARY CONTROLS PITCH MODE
atst->kaxrmode=4        # PRIMARY CONTROLS ROLL MODE
atst->kaxymode=4        # PRIMARY CONTROLS YAW MODE
atst->rdtrtime=30.00    # TIME WAIT ELAPSED
atst->ldatrmon(1)=T     # TRIM TIMER
atst->ldatrmon(2)=F     #
atst->ldatrmon(3)=F     #
atst->ldatrmon(4)=T     # ROC
atst->ldatrmon(5)=F     #
atst->ldatrmon(6)=T     # TRANSLATIONAL ACCEL
atst->ldatrmon(7)=T     # ROTATIONAL ACCEL
atst->ldatrmon(8)=F     # ROTATIONAL RATES, 0 IF COMMANDING BANK ANGLE
atst->ldatrmon(9)=F     # LONGITUDINAL CONTROL FORCE
atst->ldatrmon(10)=F    #
atst->ldatrmon(11)=F    #
atst->ldatrmon(12)=T    # GROSS WEIGHT
atst->ldatrmon(13)=T    # x CG
atst->ldatrmon(14)=F    #
atst->ldatrmon(15)=F    #
flt->ifxtrim=3          # TRIM TYPE
atst->rzpgain[0]=0.15
#atst->rzdgain[8]=0.5
#
#atst->rzdgain[8]=0.5
#atst->rzpgain[8]=1.0
#
atst->rzpgain[9] = 0.05
atst->rflkudtc=0.05
#
flt->rzcudot=  -1.069+0.34-0.11 #-0.2   # udot (ft/sec**2)    -1.069
flt->rzcvdot=   0.000   # vdot (ft/sec**2)     0.248
flt->rzcwdot=   0.000   # wdot (ft/sec**2)   -31.942
flt->rzcpdot=   0.000   # pdot (rad/sec**2)    0.000
flt->rzcqdot=   0.000   # qdot (rad/sec**2)    0.000
flt->rzcrdot=   0.000   # rdot (rad/sec**2)    0.000
flt->rzcpds =   0.000   # p (deg/sec)          1.763
flt->rzcqds =   0.000   # q (deg/sec)          0.115
flt->rzcrds =   0.000   # r (deg/sec)          0.269
#
atst->lzsslip=F         # STEADY SIDSLIP TRIM
flt->lfxstabt=F
atst->lfxailt=F
atst->lfxrudt=F
#
#  DRIVER
#  ------
#
#
atst->lzaoahld=F        #  AP AOA HOLD FLAG
atst->lzaoacf=F         #  AOA HOLD WITH CL SHIFT
atst->lzpithld=T        #  AP PITCH HOLD FLAG
atst->lzpitalthld=T     #  AP PITCH HOLD WITH ALTITUDE BIAS FLAG
atst->lzpitiashld=F     #  AP PITCH HOLD WITH IAS BIAS FLAG
atst->lzpitrochld=F     #  AP PITCH HOLD WITH ROC BIAS FLAG
atst->lzpassist=F       #  PITCH DRIVE ASSIST FLAG
atst->lzpitcf=F         #  PITCH HOLD WITH CM SHIFT
atst->lzcradalt=F       #  CMD ALT IS RAD ALT
atst->lzrochld=F        #  AP ROC HOLD FLAG
atst->lzrolhld=T        #  AP ROLL HOLD FLAG
atst->lzrassist=T       #  ROLL DRIVE ASSIST FLAG
atst->lzrolcf=F         #  ROLL HOLD WITH CR SHIFT
atst->lzbethld=F        #  AP BETA HOLD FLAG
atst->lzyassist=T       #  YAW DRIVE ASSIST FLAG
atst->lzbetcf=F         #  BETA HOLD WITH YAW MOMENT
atst->lzhdghld=T        #  AP HDG HOLD WITH RUD FLAG
atst->lzhdgcf=F         #  HEADING HOLD WITH CN SHIFT
atst->lzrwyhld=F        #  RUNWAY HEADING HOLD
atst->lzrwycf=F         #  RUNWAY HOLD WITH YAW MOMENT
atst->lzgamhld=F        #  GAMMA HOLD
atst->lznycf=F          #  NY HOLD WITH YAW MOMENT
atst->lziashld=F        #  AP IAS HOLD FLAG
atst->lziasphd=F        #  IAS HOLD VIA PITCH A/P FLAG
atst->lziascf=F         #  IAS HOLD WITH CD SHIFT
atst->lzmachld=F        #  AP MACH HOLD FLAG
atst->lzfnhld=F         #  AP FN HOLD FLAG
atst->lzfnudothld=F     #  FN HLD WITH UDOT BIAS
atst->lzfniashld=F      #  FN HLD WITH IAS BIAS
atst->lzn1hld=F         #  AP N1 HOLD FLAG
flt->lztqhld=F          #  TORQUE HOLD
atst->lztqudothld=F     #  TORQUE HOLD WITH UDOT BIAS
atst->lztassist=F       #  TORQUE HOLD THORTTLE ASSIST
atst->lzeprhld=F        #  AP EPR HOLD FLAG
atst->lzreverse(1)=F    #  REVERSE THRUST FLAG
atst->lzreverse(2)=F    #  REVERSE THRUST FLAG
atst->lzbrknx=F         #  NX BRAKE HOLD
atst->lzbphld=F         #  BRAKE PRESS HOLD WITH PEDAL
atst->lzflare=F         #  FLARE MODE
#
#  BEGIN TEST
#  ----------
#
flt->rdatime=  0.00
flt->rdatend= 80.00
atst->ldahisty=T
atst->ldaplot=T
atst->kdarate=1
#
atst->ldaprint=T
WAIT 1
flt->lfxfastm=T
flt->rfxpitcm=  -0.26   # theta (deg)
flt->rfxrolcm=   0.66   # phi (deg)
flt->rfxhdgcm=  75.23+flt->magvar #   0.00   # hdg (deg)
#
WAIT 1
#
flt->dmd_cg_in=203.221     # Center of Gravity (in)
flt->fsetcg=T
flt->fsetgw=T
P '$AUTO/atg/trim_start.pag'
flt->ldatston=T
flt->frz=F
mx->vib_ena = 0         # turn off vibes
#
WAIT flt->ldatrimd T
WAIT atst->ldadoprt F
#
#  TEST COMPLETE
#  -------------
#
WAIT atst->ldatstip F
WAIT (atst->kdaiocmd == 0)
#
p '$AUTO/atg/tst_end.pag'
x
