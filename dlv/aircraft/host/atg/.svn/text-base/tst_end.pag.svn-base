################################################################################
* %OPLICENSE%                                                                  *
*             RESTRICTED PROPRIETARY SOFTWARE AND DATA                         *
*                                                                              *
* This software and the data incorporated herein is licensed, not sold,        *
* and is protected by Federal and State copyright and related intellectual     *
* property laws. It may not be disclosed, copied, reversed engineered or used  *
* in any manner except in strict accordance with the terms and conditions of   *
* a certain License granted by OPINICUS to FLYRIGHT HOLDINGS INC. and only on  *
* the one King Air B350/200 simulator to which it relates. Sale, lease or      *
* other transfer of the simulator does not authorize the transferee to use     *
* this software and the data incorporated herein unless strict compliance with *
* the terms of the License referenced above has been met. A copy of the        *
* License is available from OPINICUS upon request. Third party licensed        *
* proprietary data may also be incorporated herein and is subject to the       *
* terms of those licenses.                                                     *
* %OPLICENSE%                                                                  * 
################################################################################

#
atst->lixindis=0

iosrx->iih_aselectprofile=0
iosrx->iih_ashearactivate=0

# Tire Failure Inhibit
flt->ftfinh=0

# BFC_HINGE_TUNER[:]=0

# Engine Lever Settings
eng->ecl[:]=30.0
eng->prla[:]=40.00
if (!flt.fbwow) {
  eng.ebstart[:]=1
}
# wait 5
# elec.gen_sw[:] = 2
# elec->line_contact[:] = 1
# wait 1
# elec.gen_sw[:] = 0
# EBSTART[:] = 0

# Environment Settings
flt->fdemwsp=0.0
flt->fsetwsp=1
flt->fdemwdir=0.0
flt->fsetwdir=1
flt->fvarwind=0
flt->ufsigmn=1

#elec.atg_gen_reset = 1
#flt.brk_trq_tuner  = 0
#flt.brk_press_ovrd = 0
#LXDI020430=0
#LXDI020431=0
#LXDI020432=0
#LXDI020433=0

# Runway Conditions
flt->fdemrwc=0
flt->fsetrwc=1
wait 1
flt->fsetrwc=0

# Crash Inhibit
flt->fcrinh=0
flt->fdemyd=0

# Clear Test Title
atst->tst_title[:]=0
# Clear Project
atst->project[:]=0
# Clear Aircrfat
atst->aircraft[:]=0
# Clear FAA Test Number
atst->faa_tstno[:]=0
# Clear Engine Type
atst->eng_type[:]=0
# Clear Flight Test File
atst->ft_file[:]=0
#
# atst->kdabferr[:]=0

# Visual Settings
vis->latency_test=0
vis->io.test_pattern=0
vis->zero_channels=0
vis->atg_side_offset=0
vis->atg_end_offset=0

if(vis->atg_chg_sound_flag) {
   comm->from_ios.sound_level=vis->atg_sound_lvl
   vis->atg_chg_sound_flag=0
}

# Motion Test Input Cleanup
mx->tstinput=0
mx->tst.ax= 0.0
mx->tst.ay= 0.0
mx->tst.az= 0.0
mx->i_pdot= 0.0
mx->i_qdot= 0.0
mx->i_rdot= 0.0
mx->i_u   = 0.0
mx->vib_ena = 1  #turn on vibs
#
# if((flt->fvcal>10.0 && flt->fhggnd>10.0) && !vis->vgs) {
#    c flt->fuea 200.0
# }
vis->vgs=0
#
# Clear All Windshear
iosrx->iih_aselectprofile=0
iosrx->iih_ashearactivate=0
#
