################################################################################
* %OPLICENSE%                                                                  *
*             RESTRICTED PROPRIETARY SOFTWARE AND DATA                         *
*                                                                              *
* This software and the data incorporated herein is licensed, not sold,        *
* and is protected by Federal and State copyright and related intellectual     *
* property laws. It may not be disclosed, copied, reversed engineered or used  *
* in any manner except in strict accordance with the terms and conditions of   *
* a certain License granted by OPINICUS to FLYRIGHT HOLDINGS INC. and only on  *
* the one King Air B350/200 simulator to which it relates. Sale, lease or      *
* other transfer of the simulator does not authorize the transferee to use     *
* this software and the data incorporated herein unless strict compliance with *
* the terms of the License referenced above has been met. A copy of the        *
* License is available from OPINICUS upon request. Third party licensed        *
* proprietary data may also be incorporated herein and is subject to the       *
* terms of those licenses.                                                     *
* %OPLICENSE%                                                                  * 
################################################################################
#
#  TITLE:       Motion System Repeatability - Heave in Air
#  -----
#
#  PROJECT:     BEECHCRAFT KING AIR 350
#  -------
#
#  AIRCRAFT:    B350
#  --------
#
#  CRITERIA:    3.d.3
#  --------
#
#  TEST NUMBER: f03403000000000
#  -----------
#
##############################################
#
#  PRELIMINARY
#  -----------
#
# FAA:               3.d.3
# Maneuver:          Motion System Repeatability - Heave in Air
# File No:           N/A
# Flight Test Date:  N/A
# Start FT_Time:     N/A
# Stop  FT_Time:     N/A
# Start OP_Time:     226.99
# Stop  OP_Time:     266.99
# -------------------------------------
# Flap:              0.00
# Gear:              1.00
# Weight:            12756.2
# Hp:                387.0
# Kcas:              0.00
# Mach:              0.0001
# TAT:               -18.429
#
WAIT 1
C atst->kdatstno 3403000000000
#
strcpy atst->project   'Beechcraft B350 Training Device - v.'
strcpy atst->aircraft  'B350'
strcpy atst->tst_title 'Motion System Repeatability - Heave in Air'
strcpy atst->faa_tstno '3.d.3.a'
strcpy atst->eng_type  'PT6A-60A'
strcpy atst->ft_file   'N/A'
#
atst->ft_time_hack[0]=8.0
atst->ft_time_hack[1]=44.0
#
#
WAIT 1
#
# Include Flight Test Trim Values
# -------------------------------
p '$AUTO/atg/ft_f03403000000000.pag'
#
WAIT RESET
WAIT DEFAULT 1000
AUTOCHANGE NO
WAIT 1
WAIT sim->config_inprogress F
flt->frz=T
# flt->fmwtcg=T
flt->nposfrz=T
flt->ftgactiv=T
atst->lixindis=T
#
#  PARAMETER PLOT
#  --------------
#
PLOT BEGIN
PLOT POINTS
PLOT TITLE 'Motion Repeatability - Heave in Air'
PLOT FILE 'f03403000000000.PLT'
P '$AUTO/plot/motion_repeat.plt'
PLOT END
#
#  LFI TABLES
#  ----------
#
LFI RESET
P '$AUTO/lfi_atg/motion_heave.lfi'
#
##############################################
#
#  WEIGHT AND BALANCE
#  ------------------
#
# Total Fuel Load divided evenly between left and right
fuel->fdemtnk[0]= 1222.1  # Left Total  = 1195.8 (Main=1195.8,Aux=  0.0)
fuel->fdemtnk[1]= 1222.1  # Right Total = 1248.4 (Main=1248.4,Aux=  0.0)
fuel->fsetfuel=T
flt->fdemgw=     12756.2  # Gross Weight (lbs)
flt->fsetgw=T
wait 20 flt->fwgrss 12556.2 12956.2
flt->dmd_cg_in=197.923     # Center of Gravity (in)
flt->fsetcg=T
flt->fsetgw=T
flt->fdemixx=     30240.7  # Ixx (slug-ft^2)
flt->fdemiyy=     17939.5  # Iyy (slug-ft^2)
flt->fdemizz=     44828.8  # Izz (slug-ft^2)
flt->fdemixz=      1850.5  # Ixz (slug-ft^2)
# flt->fsetiner=T            # Set Inertias
#
#  ENVIRONMENT
#  -----------
#
flt->fdembaro=  29.92  # barometric pressure
WAIT 1
flt->fsetbaro=T
flt->fdemtemp= -18.28  # oat (deg C)
WAIT 1
flt->fsetoat=T
flt->fdemwdir=   0.00  # hdg (deg)
flt->fdemwsp=    0.00  # Wind Speed
flt->fsetwdir=T
flt->fsetwsp=T
flt->fdemrwc=0         # runway conditions, dry
flt->fsetrwc=T
#
#  FLIGHT CONDITIONS
#  -----------------
#
flt->fdemalt=  1000.0    # alt (ft)
flt->fsetalt=1
wait 20 flt->fhp   950.0  1050.0
flt->fsetalt=0
flt->fdemhter=381.0    # 0.0 field elevation (ft)
flt->fsethter=T
flt->fmtor=T
flt->fipfld=T
WAIT 2
atst->ftdlat=     0.00   # lat
atst->ftdlon=     0.00   # long
atst->ftdhdg=   139.12 +   0.00   # mag hdg + mag var
flt->fdist=       0.0   # takeoff distance
flt->fgyecg=      0.0   # east CG position from reference point
flt->fgxecg=      0.0   # north CG position from reference point
#
atst->rtcvg=      0.00   #      0.00   # ve (kts)
# flt->rzcroc=      7.11   # roc (fpm)
# flt->rfxpitcm=    1.98   # theta (deg)
# flt->rfxrolcm=    0.00   #   0.57   # phi (deg)
flt->rfxhdgcm= 139.12+  flt->magvar   # hdg (deg)
#
# Generic Runway Setup
vis->atg_sel_hdg=atst->ftdhdg
vis->atg_sel_elev=flt->fdemhter
P '$AUTO/atg/ftakeoff.pag'
#
#  AIRCRAFT CONFIGURATION
#  ----------------------
#
ctl->fsetflap=T
ctl->fdemflap=   0.00  # flap (deg)
ctl->fsetgear=T
ctl->fdemgear=   1.00  # gear
wait 20 ctl->gear.avg_pos  0.90  1.10
wait 20 ctl->flap.avg_surf_pos -0.50 0.50 # (deg)
#
flt->fdemstab=    4.065   #   4.065 longitudinal trim pos.
atst->fdemrtab=   0.702   #   0.702 rudder trim pos.
atst->fdematab=   0.405   #   0.405 aileron trim pos.
atst->fsetstab=T
atst->fsetrtab=T
atst->fsetatab=T
atst->fsetydmp=F  # Yaw Damper
#
#  ENGINES
#  -------
#
flt->rzcn1[0]=       0.000  # Engine 1 N1 (perc)
flt->rzcn1[1]=       0.000  # Engine 2 N1 (perc)
atst->rzcn2[0]=      0.000  # Engine 1 N2 (perc)
atst->rzcn2[1]=      0.000  # Engine 2 N2 (perc)
eng->dmd_prla[0]=   43.66   #    42.91 Engine 1 PROPELLER LEVER ANGLE (deg)
eng->dmd_prla[1]=   43.66  #    44.41 Engine 2 PROPELLER LEVER ANGLE (deg)
eng->dmd_cl[0]=     19.94   #    19.59 Engine 1 CONDITION LEVER ANGLE (deg)
eng->dmd_cl[1]=     19.94   #    20.28 Engine 2 CONDITION LEVER ANGLE (deg)
eng->fdemeng[0]=    18.00   #    51.33 Engine 1 TLA (deg)
eng->fdemeng[1]=    18.00   #    51.95 Engine 2 TLA (deg)
atst->rzctq[0]=   0000.00   # Engine 1 Torque (ft-lb)
atst->rzctq[1]=   0000.00   # Engine 2 Torque (ft-lb)
wait 1
#
#  SYSTEMS
#  -------
#
hyd.In.di_park_brake=F
atst->fdemtoeb=  25.0  #16.65  # Cmd Brake Pos
atst->fdemtoer=  25.0  #27.43  # Cmd Brake Pos
atst->fsettoeb=T
atst->fsettoer=T
atst->rzcbp[0]= 416.98  # Cmd Brk Press
atst->rzcbp[1]= 480.09  # Cmd Brk Press
#
##############################################
#
#  TRIMMER
#  -------
#
wait 3
atst->kaxpmode=3        # PRIMARY CONTROLS PITCH MODE
atst->kaxrmode=4        # PRIMARY CONTROLS ROLL MODE
atst->kaxymode=4        # PRIMARY CONTROLS YAW MODE
atst->rdtrtime=30.00    # TIME WAIT ELAPSED
atst->ldatrmon(1)=T     # TRIM TIMER
atst->ldatrmon(2)=F     #
atst->ldatrmon(3)=F     #
atst->ldatrmon(4)=T     # ROC
atst->ldatrmon(5)=F     #
atst->ldatrmon(6)=T     # TRANSLATIONAL ACCEL
atst->ldatrmon(7)=T     # ROTATIONAL ACCEL
atst->ldatrmon(8)=F     # ROTATIONAL RATES, 0 IF COMMANDING BANK ANGLE
atst->ldatrmon(9)=F     # LONGITUDINAL CONTROL FORCE
atst->ldatrmon(10)=F    #
atst->ldatrmon(11)=F    #
atst->ldatrmon(12)=T    # GROSS WEIGHT
atst->ldatrmon(13)=T    # x CG
atst->ldatrmon(14)=F    #
atst->ldatrmon(15)=F    #
flt->ifxtrim=0          # TRIM TYPE
#
flt->rzcudot=   0.000   # udot (ft/sec**2)     0.202
flt->rzcvdot=   0.000   # vdot (ft/sec**2)    -0.394
flt->rzcwdot=   0.000   # wdot (ft/sec**2)   -32.165
flt->rzcpdot=   0.000   # pdot (rad/sec**2)    0.000
flt->rzcqdot=   0.000   # qdot (rad/sec**2)    0.000
flt->rzcrdot=   0.000   # rdot (rad/sec**2)    0.000
flt->rzcpds =   0.000   # p (deg/sec)         -0.071
flt->rzcqds =   0.000   # q (deg/sec)          0.022
flt->rzcrds =   0.000   # r (deg/sec)         -0.053
atst->fdemnw = -2.664   # Dem NW Angle   -2.664
#
atst->lzsslip=F         # STEADY SIDSLIP TRIM
flt->lfxstabt=F
atst->lfxailt=F
atst->lfxrudt=F
#
#  DRIVER
#  ------
#
atst->rcxlonsk=14.********/(-20.5)  #)
#
atst->lzaoahld=F        #  AP AOA HOLD FLAG
atst->lzaoacf=F         #  AOA HOLD WITH CL SHIFT
atst->lzpithld=F        #  AP PITCH HOLD FLAG
atst->lzpitalthld=F     #  AP PITCH HOLD WITH ALTITUDE BIAS FLAG
atst->lzpitiashld=F     #  AP PITCH HOLD WITH IAS BIAS FLAG
atst->lzpitrochld=F     #  AP PITCH HOLD WITH ROC BIAS FLAG
atst->lzpassist=F       #  PITCH DRIVE ASSIST FLAG
atst->lzpitcf=F         #  PITCH HOLD WITH CM SHIFT
atst->lzcradalt=F       #  CMD ALT IS RAD ALT
atst->lzrochld=F        #  AP ROC HOLD FLAG
atst->lzrolhld=F        #  AP ROLL HOLD FLAG
atst->lzrassist=F       #  ROLL DRIVE ASSIST FLAG
atst->lzrolcf=F         #  ROLL HOLD WITH CR SHIFT
atst->lzbethld=F        #  AP BETA HOLD FLAG
atst->lzyassist=F       #  YAW DRIVE ASSIST FLAG
atst->lzbetcf=F         #  BETA HOLD WITH YAW MOMENT
atst->lzhdghld=F        #  AP HDG HOLD WITH RUD FLAG
atst->lzhdgcf=F         #  HEADING HOLD WITH CN SHIFT
atst->lzrwyhld=F        #  RUNWAY HEADING HOLD
atst->lzrwycf=F         #  RUNWAY HOLD WITH YAW MOMENT
atst->lzgamhld=F        #  GAMMA HOLD
atst->lznycf=F          #  NY HOLD WITH YAW MOMENT
atst->lziashld=F        #  AP IAS HOLD FLAG
atst->lziasphd=F        #  IAS HOLD VIA PITCH A/P FLAG
atst->lziascf=F         #  IAS HOLD WITH CD SHIFT
atst->lzmachld=F        #  AP MACH HOLD FLAG
atst->lzfnhld=F         #  AP FN HOLD FLAG
atst->lzfnudothld=F     #  FN HLD WITH UDOT BIAS
atst->lzfniashld=F      #  FN HLD WITH IAS BIAS
atst->lzn1hld=F         #  AP N1 HOLD FLAG
flt->lztqhld=F          #  TORQUE HOLD
atst->lztqudothld=F     #  TORQUE HOLD WITH UDOT BIAS
atst->lztassist=F       #  TORQUE HOLD THORTTLE ASSIST
atst->lzeprhld=F        #  AP EPR HOLD FLAG
atst->lzreverse(1)=F    #  REVERSE THRUST FLAG
atst->lzreverse(2)=F    #  REVERSE THRUST FLAG
atst->lzbrknx=F         #  NX BRAKE HOLD
atst->lzbphld=F         #  BRAKE PRESS HOLD WITH PEDAL
atst->lzflare=F         #  FLARE MODE
#
#  TEST SETUP
#  ----------
atst->fmaxsfor = 40.0
atst->fmaxwfor = 40.0
atst->fmaxpfor = 50.0
#
#  BEGIN TEST
#  ----------
#
atst->fhwelev=T
atst->fhwrud=T
#
flt->rdatime=  0.00
flt->rdatend= 36.00
atst->ldahisty=T
atst->ldaplot=T
atst->kdarate=1
#
atst->ldaprint=T
WAIT 1
flt->lfxfastm=T
# flt->rfxpitcm=   1.98   # theta (deg)
# flt->rfxrolcm=   0.57   # phi (deg)
# flt->rfxhdgcm= 139.12+  flt->magvar   # hdg (deg)
#
WAIT 1
#
flt->dmd_cg_in=197.923     # Center of Gravity (in)
flt->fsetcg=T
flt->fsetgw=T
P '$AUTO/atg/trim_start.pag'
flt->ldatston=T

# Motion Test Inputs
mx->vib_ena = 0       #turn off vibes
mx->tst.ax= 0.0
mx->tst.ay= 0.0
mx->tst.az= 0.0
mx->i_pdot= 0.0
mx->i_qdot= 0.0
mx->i_rdot= 0.0
mx->i_u   = 200.0
mx->tstinput=1
mx->aog=0

flt->frz=F

#
WAIT flt->ldatrimd T
WAIT atst->ldadoprt F
#
#  TEST COMPLETE
#  -------------
#
WAIT atst->ldatstip F
WAIT (atst->kdaiocmd == 0)
#
atst->fhwelev=F
atst->fhwrud=F
p '$AUTO/atg/tst_end.pag'
x
