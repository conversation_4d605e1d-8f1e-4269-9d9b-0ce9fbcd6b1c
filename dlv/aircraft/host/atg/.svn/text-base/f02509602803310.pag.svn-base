################################################################################
* %OPLICENSE%                                                                  *
*             RESTRICTED PROPRIETARY SOFTWARE AND DATA                         *
*                                                                              *
* This software and the data incorporated herein is licensed, not sold,        *
* and is protected by Federal and State copyright and related intellectual     *
* property laws. It may not be disclosed, copied, reversed engineered or used  *
* in any manner except in strict accordance with the terms and conditions of   *
* a certain License granted by OPINICUS to FLYRIGHT HOLDINGS INC. and only on  *
* the one King Air B350/200 simulator to which it relates. Sale, lease or      *
* other transfer of the simulator does not authorize the transferee to use     *
* this software and the data incorporated herein unless strict compliance with *
* the terms of the License referenced above has been met. A copy of the        *
* License is available from OPINICUS upon request. Third party licensed        *
* proprietary data may also be incorporated herein and is subject to the       *
* terms of those licenses.                                                     *
* %OPLICENSE%                                                                  *
################################################################################
#
#  TITLE:       Directional Control, Asymmetric Thrust
#  -----
#
#  PROJECT:     BEECHCRAFT KING AIR 350
#  -------
#
#  AIRCRAFT:    B3501
#  --------
#
#  CRITERIA:    2.e.9
#  --------
#
#  TEST NUMBER: f02509602803310
#  -----------
#
##############################################
#
#  PRELIMINARY
#  -----------
#
# FAA:               2.e.9
# Maneuver:          Directional Control, Asymmetric Thrust
# File No:           k1701028_cmb.csv
# Flight Test Date:  01/13/01
# Start FT_Time:     0.00
# Stop  FT_Time:     59.98
# Start OP_Time:     31.00
# Stop  OP_Time:     45.00
# -------------------------------------
# Flap:              35.00
# Gear:              1.00
# Weight:            11829.8
# Hp:                417.6
# Kcas:              97.27
# Mach:              0.1468
# TAT:               -16.787
#
WAIT 1
C atst->kdatstno 2509602803310
#
strcpy atst->project   'Beechcraft B350 Training Device - v.'
strcpy atst->aircraft  'B350'
strcpy atst->tst_title 'Directional Control, Asymmetric Reverse Thrust'
strcpy atst->faa_tstno '2.e.9.a'
strcpy atst->eng_type  'PT6A-60A'
strcpy atst->ft_file   'k1701028_cmb.csv'
#
atst->ft_time_hack[0]=31.0
atst->ft_time_hack[1]=45.0
#
WAIT 1
#
# Include Flight Test Trim Values
# -------------------------------
p '$AUTO/atg/ft_f02509602803310.pag'
#
WAIT RESET
WAIT DEFAULT 1000
AUTOCHANGE NO
WAIT 1
WAIT sim->config_inprogress F
flt->frz=T
# flt->fmwtcg=T
flt->nposfrz=T
flt->ftgactiv=T
atst->lixindis=T
#
#  PARAMETER PLOT
#  --------------
#
PLOT BEGIN
PLOT POINTS
PLOT TITLE 'Directional Control, Asymmetric Reverse Thrust'
PLOT FILE 'f02509602803310.PLT'
P '$AUTO/plot/f02509602803310.plt'
PLOT END
#
#  LFI TABLES
#  ----------
#
LFI RESET
P '$AUTO/lfi_atg/f02509602803310.lfi'
#
##############################################
# Reposition to Home Airport Location
P '$AUTO/atg/init_takeoff_repos.pag'
#
#  WEIGHT AND BALANCE
#  ------------------
#
# Total Fuel Load divided evenly between left and right
fuel->fdemtnk[0]= 758.9  # Left Total  = 760.7 (Main=760.7,Aux=  0.0)
fuel->fdemtnk[1]= 758.9  # Right Total = 757.1 (Main=757.1,Aux=  0.0)
fuel->fsetfuel=T
flt->fdemgw=     11829.8  # Gross Weight (lbs)
flt->fsetgw=T
wait 20 flt->fwgrss 11629.8 12029.8
flt->dmd_cg_in=197.604     # Center of Gravity (in)
flt->fsetcg=T
flt->fsetgw=T
flt->fdemixx=     21350.5  # Ixx (slug-ft^2)
flt->fdemiyy=     17917.5  # Iyy (slug-ft^2)
flt->fdemizz=     35919.0  # Izz (slug-ft^2)
flt->fdemixz=      1847.5  # Ixz (slug-ft^2)
# flt->fsetiner=T            # Set Inertias
#
#  ENVIRONMENT
#  -----------
#
flt->fdembaro=  29.92  # barometric pressure
WAIT 1
flt->fsetbaro=T
flt->fdemtemp= -17.74  # oat (deg C)
WAIT 1
flt->fsetoat=T
flt->fdemwdir=  71.00  #    0.00 # Wind Direction
flt->fdemwsp=    7.00  #    0.00 # Wind Speed
flt->fsetwdir=T
flt->fsetwsp=T
flt->fdemrwc=0         # runway conditions, dry
flt->fsetrwc=T
#
#  FLIGHT CONDITIONS
#  -----------------
#
flt->fdemalt=  1000.0    # alt (ft)
flt->fsetalt=1
wait 20 flt->fhp   950.0  1050.0
flt->fsetalt=0
flt->fdemhter=409.0    # 0.0 field elevation (ft)
flt->fsethter=T
flt->fmtor=T
flt->fipfld=T
WAIT 2
atst->ftdlat=     0.00   # lat
atst->ftdlon=     0.00   # long
atst->ftdhdg=    71.31 +   flt->magvar   # mag hdg + mag var
flt->fdist=       0.0   # takeoff distance
flt->fgyecg=      0.0   # east CG position from reference point
flt->fgxecg=      0.0   # north CG position from reference point
#
atst->rtcvg=     86.00   # ve (kts)
# flt->rzcroc=    842.63   # roc (fpm)
# flt->rfxpitcm=    0.00   # 1.29   # theta (deg)
flt->rfxrolcm=    0.00   # 0.31   # phi (deg)
flt->rfxhdgcm=  71.31+   flt->magvar   # hdg (deg)
#
# Generic Runway Setup
vis->atg_sel_hdg=atst->ftdhdg+8.0
vis->atg_sel_elev=flt->fdemhter
P '$AUTO/atg/ftakeoff.pag'
#
#  AIRCRAFT CONFIGURATION
#  ----------------------
#
ctl->fsetflap=T
ctl->fdemflap=   2.00  # flap (deg)
ctl->fsetgear=T
ctl->fdemgear=   1.00  # gear
wait 20 ctl->gear.avg_pos  0.90  1.10
wait 20 ctl->flap.avg_surf_pos 34.50 35.50 # (deg)
#
flt->fdemstab=    8.490   #   8.490 longitudinal trim pos.
atst->fdemrtab=  -0.963   #  -0.963 rudder trim pos.
atst->fdematab=   0.348   #   0.348 aileron trim pos.
atst->fsetstab=T
atst->fsetrtab=T
atst->fsetatab=T
atst->fsetydmp=F  # Yaw Damper
#
#  ENGINES
#  -------
#
flt->rzcn1[0]=      64.295  # Engine 1 N1 (perc)
flt->rzcn1[1]=      62.971  # Engine 2 N1 (perc)
atst->rzcn2[0]=   1254.424  # Engine 1 N2 (perc)
atst->rzcn2[1]=   1255.385  # Engine 2 N2 (perc)
eng->dmd_prla[0]=   43.97   #    43.27 Engine 1 PROPELLER LEVER ANGLE (deg)
eng->dmd_prla[1]=   43.97   #    44.67 Engine 2 PROPELLER LEVER ANGLE (deg)
eng->dmd_cl[0]=     19.96   #    19.60 Engine 1 CONDITION LEVER ANGLE (deg)
eng->dmd_cl[1]=     19.96   #    20.31 Engine 2 CONDITION LEVER ANGLE (deg)
eng->fdemeng[0]=    21.00   #    20.92 Engine 1 TLA (deg)
eng->fdemeng[1]=    21.00   #    20.95 Engine 2 TLA (deg)
atst->rzctq[0]=    -23.45   # Engine 1 Torque (ft-lb)
atst->rzctq[1]=      1.41   # Engine 2 Torque (ft-lb)
eng->ebstart[:]=1           # Faststart both engines
wait 1
#
#  SYSTEMS
#  -------
#
hyd.In.di_park_brake=F
atst->fdemtoeb=   0.00  # Cmd Brake Pos
atst->fdemtoer=   0.00  # Cmd Brake Pos
atst->fsettoeb=T
atst->fsettoer=T
atst->rzcbp[0]=   0.00  # Cmd Brk Press
atst->rzcbp[1]=   0.00  # Cmd Brk Press
#
##############################################
#
#  TRIMMER
#  -------
#
wait 3
atst->kaxpmode=4        # PRIMARY CONTROLS PITCH MODE
atst->kaxrmode=4        # PRIMARY CONTROLS ROLL MODE
atst->kaxymode=4        # PRIMARY CONTROLS YAW MODE
atst->rdtrtime=30.00    # TIME WAIT ELAPSED
atst->ldatrmon(1)=T     # TRIM TIMER
atst->ldatrmon(2)=F     #
atst->ldatrmon(3)=F     #
atst->ldatrmon(4)=F     # ROC
atst->ldatrmon(5)=F     #
atst->ldatrmon(6)=F     # TRANSLATIONAL ACCEL
atst->ldatrmon(7)=F     # ROTATIONAL ACCEL
atst->ldatrmon(8)=F     # ROTATIONAL RATES, 0 IF COMMANDING BANK ANGLE
atst->ldatrmon(9)=F     # LONGITUDINAL CONTROL FORCE
atst->ldatrmon(10)=F    #
atst->ldatrmon(11)=F    #
atst->ldatrmon(12)=T    # GROSS WEIGHT
atst->ldatrmon(13)=T    # x CG
atst->ldatrmon(14)=F    #
atst->ldatrmon(15)=F    #
flt->ifxtrim=0          # TRIM TYPE
#
flt->rzcudot=  -7.530   # udot (ft/sec**2)    -6.040
flt->rzcvdot=   0.000   # vdot (ft/sec**2)     0.055
flt->rzcwdot=   0.000   # wdot (ft/sec**2)   -30.094
flt->rzcpdot=   0.000   # pdot (rad/sec**2)    0.000
flt->rzcqdot=   0.000   # qdot (rad/sec**2)    0.000
flt->rzcrdot=   0.000   # rdot (rad/sec**2)    0.000
flt->rzcpds =   0.000   # p (deg/sec)         -0.584
flt->rzcqds =   0.000   # q (deg/sec)          0.998
flt->rzcrds =   0.000   # r (deg/sec)         -0.200
atst->fdemnw =  0.685   # Dem NW Angle    0.685
#
atst->lzsslip=F         # STEADY SIDSLIP TRIM
flt->lfxstabt=F
atst->lfxailt=F
atst->lfxrudt=F
#
#  DRIVER
#  ------
#
atst->rcxlonsk=-4.*********/(-20.5)  #)
atst->rcxruped=-0.01
#
atst->lzaoahld=F        #  AP AOA HOLD FLAG
atst->lzaoacf=F         #  AOA HOLD WITH CL SHIFT
atst->lzpithld=F        #  AP PITCH HOLD FLAG
atst->lzpitalthld=F     #  AP PITCH HOLD WITH ALTITUDE BIAS FLAG
atst->lzpitiashld=F     #  AP PITCH HOLD WITH IAS BIAS FLAG
atst->lzpitrochld=F     #  AP PITCH HOLD WITH ROC BIAS FLAG
atst->lzpassist=F       #  PITCH DRIVE ASSIST FLAG
atst->lzpitcf=F         #  PITCH HOLD WITH CM SHIFT
atst->lzcradalt=F       #  CMD ALT IS RAD ALT
atst->lzrochld=F        #  AP ROC HOLD FLAG
atst->lzrolhld=F        #  AP ROLL HOLD FLAG
atst->lzrassist=F       #  ROLL DRIVE ASSIST FLAG
atst->lzrolcf=F         #  ROLL HOLD WITH CR SHIFT
atst->lzbethld=F        #  AP BETA HOLD FLAG
atst->lzyassist=F       #  YAW DRIVE ASSIST FLAG
atst->lzbetcf=F         #  BETA HOLD WITH YAW MOMENT
atst->lzhdghld=F        #  AP HDG HOLD WITH RUD FLAG
atst->lzhdgcf=F         #  HEADING HOLD WITH CN SHIFT
atst->lzrwyhld=F        #  RUNWAY HEADING HOLD
atst->lzrwycf=F         #  RUNWAY HOLD WITH YAW MOMENT
atst->lzgamhld=F        #  GAMMA HOLD
atst->lznycf=F          #  NY HOLD WITH YAW MOMENT
atst->lziashld=F        #  AP IAS HOLD FLAG
atst->lziasphd=F        #  IAS HOLD VIA PITCH A/P FLAG
atst->lziascf=F         #  IAS HOLD WITH CD SHIFT
atst->lzmachld=F        #  AP MACH HOLD FLAG
atst->lzfnhld=F         #  AP FN HOLD FLAG
atst->lzfnudothld=F     #  FN HLD WITH UDOT BIAS
atst->lzfniashld=F      #  FN HLD WITH IAS BIAS
atst->lzn1hld=F         #  AP N1 HOLD FLAG
flt->lztqhld=F          #  TORQUE HOLD
atst->lztqudothld=F     #  TORQUE HOLD WITH UDOT BIAS
atst->lztassist=T       #  TORQUE HOLD THORTTLE ASSIST
atst->lzeprhld=F        #  AP EPR HOLD FLAG
atst->lzreverse(1)=F    #  REVERSE THRUST FLAG
atst->lzreverse(2)=F    #  REVERSE THRUST FLAG
atst->lzbrknx=F         #  NX BRAKE HOLD
atst->lzbphld=F         #  BRAKE PRESS HOLD WITH PEDAL
atst->lzflare=F         #  FLARE MODE
#
#  BEGIN TEST
#  ----------
#
flt->rdatime=  0.00
flt->rdatend= 14.00
atst->ldahisty=T
atst->ldaplot=T
atst->kdarate=1
#
atst->ldaprint=T
WAIT 1
flt->lfxfastm=T
# flt->rfxpitcm=    0.00   # 1.29   # theta (deg)
flt->rfxrolcm=    0.00   # 0.31   # phi (deg)
flt->rfxhdgcm=  71.31+   flt->magvar   # hdg (deg)
#
WAIT 1
#
flt->dmd_cg_in=197.604     # Center of Gravity (in)
flt->fsetcg=T
flt->fsetgw=T
P '$AUTO/atg/trim_start.pag'
flt->ldatston=T
flt->frz=F
#
WAIT flt->ldatrimd T
WAIT atst->ldadoprt F
#
#  TEST COMPLETE
#  -------------
#
WAIT atst->ldatstip F
WAIT (atst->kdaiocmd == 0)
#
p '$AUTO/atg/tst_end.pag'
x
