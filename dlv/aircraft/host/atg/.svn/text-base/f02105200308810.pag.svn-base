################################################################################
* %OPLICENSE%                                                                  *
*             RESTRICTED PROPRIETARY SOFTWARE AND DATA                         *
*                                                                              *
* This software and the data incorporated herein is licensed, not sold,        *
* and is protected by Federal and State copyright and related intellectual     *
* property laws. It may not be disclosed, copied, reversed engineered or used  *
* in any manner except in strict accordance with the terms and conditions of   *
* a certain License granted by OPINICUS to FLYRIGHT HOLDINGS INC. and only on  *
* the one King Air B350/200 simulator to which it relates. Sale, lease or      *
* other transfer of the simulator does not authorize the transferee to use     *
* this software and the data incorporated herein unless strict compliance with *
* the terms of the License referenced above has been met. A copy of the        *
* License is available from OPINICUS upon request. Third party licensed        *
* proprietary data may also be incorporated herein and is subject to the       *
* terms of those licenses.                                                     *
* %OPLICENSE%                                                                  * 
################################################################################
#
#  TITLE:       Rudder Pedal Steering Calibration
#  -----
#
#  PROJECT:     BEECHCRAFT KING AIR 350
#  -------
#
#  AIRCRAFT:    B350
#  --------
#
#  CRITERIA:    2.a.5
#  --------
#
#  TEST NUMBER: f02105200308810
#  -----------
#
##############################################
#
#  PRELIMINARY
#  -----------
#
# FAA:               2.a.5
# Maneuver:          Rudder Pedal Steering Calibration
# File No:           k4101003_cmb.csv
# Flight Test Date:  01/29/01
# Start FT_Time:     0.00
# Stop  FT_Time:     198.98
# Start OP_Time:     0.00
# Stop  OP_Time:     198.98
# -------------------------------------
# Flap:              0.00
# Gear:              1.00
# Weight:            13256.7
# Hp:                385.7
# Kcas:              0.00
# Mach:              0.0001
# TAT:               -6.914
#
WAIT 1
C atst->kdatstno 2105200308810
WAIT 1
#
# Include Flight Test Trim Values
# -------------------------------
p '$AUTO/atg/ft_f02105200308810.pag'
#
WAIT RESET
WAIT DEFAULT 1000
AUTOCHANGE NO
WAIT 1
WAIT sim->config_inprogress F
flt->frz=T
# flt->fmwtcg=T
flt->nposfrz=T
flt->ftgactiv=T
atst->lixindis=T
#
#  PARAMETER PLOT
#  --------------
#
PLOT BEGIN
PLOT POINTS
PLOT TITLE 'Rudder Pedal Steering Calibration'
PLOT FILE 'f02105200308810.PLT'
P '$AUTO/plot/f02105200308810.plt'
PLOT END
#
#  LFI TABLES
#  ----------
#
LFI RESET
P '$AUTO/lfi_atg/f02105200308810.lfi'
#
##############################################
#
#  WEIGHT AND BALANCE
#  ------------------
#
fuel->fdemtnk[0]=    1440.6  # Main Tank 1 (lbs)
fuel->fdemtnk[1]=    1504.1  # Main Tank 2 (lbs)
fuel->fsetfuel=T
flt->fdemgw=     13256.7  # Gross Weight (lbs)
flt->fsetgw=T
wait 20 flt->fwgrss 13056.7 13456.7
flt->dmd_cg_in=198.633     # Center of Gravity (in)
flt->fsetcg=T
flt->fsetgw=T
flt->fdemixx=     31297.9  # Ixx (slug-ft^2)
flt->fdemiyy=     18004.1  # Iyy (slug-ft^2)
flt->fdemizz=     45907.1  # Izz (slug-ft^2)
flt->fdemixz=      1824.2  # Ixz (slug-ft^2)
# flt->fsetiner=T            # Set Inertias
#
#  ENVIRONMENT
#  -----------
#
flt->fdembaro=  29.92  # barometric pressure
WAIT 1
flt->fsetbaro=T
flt->fdemtemp=  -6.76  # oat (deg C)
WAIT 1
flt->fsetoat=T
flt->fdemwdir=   0.00  #    0.00 # Wind Direction
flt->fdemwsp=    0.00  #    0.00 # Wind Speed
flt->fsetwdir=T
flt->fsetwsp=T
flt->fdemrwc=0         # runway conditions, dry
flt->fsetrwc=T
#
#  FLIGHT CONDITIONS
#  -----------------
#
# flt->fdemhter=0.0      # 0.0 field elevation (ft)
# flt->fsethter=T
# flt->fdemalt=  385.7    # alt (ft)
# flt->fsetalt=1
# wait 20 flt->fhp   335.7   435.7
# flt->fdemhter=0.0      # 0.0 field elevation (ft)
# flt->fsethter=T
# atst->rzcalthp=  385.7   # demanded alt (ft)
flt->fmtor=T
flt->fipfld=T
WAIT 2
atst->ftdlat=     0.00   # lat
atst->ftdlon=     0.00   # long
atst->ftdhdg=   318.82 +   0.00   # mag hdg + mag var
flt->fdist=       0.0   # takeoff distance
flt->fgyecg=      0.0   # east CG position from reference point
flt->fgxecg=      0.0   # north CG position from reference point
#
atst->rtcvg=      0.00   #      0.75   # ve (kts)
# flt->rzcroc=    275.06   # roc (fpm)
# flt->rfxpitcm=    2.33   # theta (deg)
# flt->rfxrolcm=    0.00   #  -0.34   # phi (deg)
# flt->rfxhdgcm= 318.82+   0.00   # hdg (deg)
#
#  AIRCRAFT CONFIGURATION
#  ----------------------
#
ctl->fsetflap=T
ctl->fdemflap=   0.00  # flap (deg)
ctl->fsetgear=T
ctl->fdemgear=   1.00  # gear
wait 20 ctl->gear.avg_pos  0.90  1.10
wait 20 ctl->flap.avg_surf_pos -0.50 0.50 # (deg)
#
flt->fdemstab=    4.082   #   4.082 longitudinal trim pos.
atst->fdemrtab=  -0.052   #  -0.052 rudder trim pos.
atst->fdematab=   0.631   #   0.631 aileron trim pos.
atst->fsetstab=T
atst->fsetrtab=T
atst->fsetatab=T
atst->fsetydmp=F  # Yaw Damper
#
#  ENGINES
#  -------
#
flt->rzcn1[0]=      61.578  # Engine 1 N1 (perc)
flt->rzcn1[1]=      62.083  # Engine 2 N1 (perc)
atst->rzcn2[0]=   1032.024  # Engine 1 N2 (perc)
atst->rzcn2[1]=   1046.200  # Engine 2 N2 (perc)
eng->dmd_prla[0]=   43.17   # Engine 1 PROPELLER LEVER ANGLE (deg)
eng->dmd_prla[1]=   44.80   # Engine 2 PROPELLER LEVER ANGLE (deg)
eng->dmd_cl[0]=     19.75   # Engine 1 CONDITION LEVER ANGLE (deg)
eng->dmd_cl[1]=     20.25   # Engine 2 CONDITION LEVER ANGLE (deg)
eng->fdemeng[0]=    32.39   # Engine 1 TLA (deg)
eng->fdemeng[1]=    32.62   # Engine 2 TLA (deg)
atst->rzctq[0]=    107.20   # Engine 1 Torque (ft-lb)
atst->rzctq[1]=    147.55   # Engine 2 Torque (ft-lb)
wait 1
#
#  SYSTEMS
#  -------
#
hyd.In.di_park_brake=F
atst->fdemtoeb=   0.07  # Cmd Brake Pos
atst->fdemtoer=   0.59  # Cmd Brake Pos
atst->fsettoeb=T
atst->fsettoer=T
atst->rzcbp[0]= 287.60  # Cmd Brk Press
atst->rzcbp[1]= 239.77  # Cmd Brk Press
#
##############################################
#
#  TRIMMER
#  -------
#
wait 3
atst->kaxpmode=3        # PRIMARY CONTROLS PITCH MODE
atst->kaxrmode=3        # PRIMARY CONTROLS ROLL MODE
atst->kaxymode=3        # PRIMARY CONTROLS YAW MODE
atst->rdtrtime=30.00    # TIME WAIT ELAPSED
atst->ldatrmon(1)=T     # TRIM TIMER
atst->ldatrmon(2)=F     #
atst->ldatrmon(3)=F     #
atst->ldatrmon(4)=F ##T     # ROC
atst->ldatrmon(5)=F     #
atst->ldatrmon(6)=F ##T     # TRANSLATIONAL ACCEL
atst->ldatrmon(7)=F ##T     # ROTATIONAL ACCEL
atst->ldatrmon(8)=F     # ROTATIONAL RATES, 0 IF COMMANDING BANK ANGLE
atst->ldatrmon(9)=F     # LONGITUDINAL CONTROL FORCE
atst->ldatrmon(10)=F    #
atst->ldatrmon(11)=F    #
atst->ldatrmon(12)=T    # GROSS WEIGHT
atst->ldatrmon(13)=T    # x CG
atst->ldatrmon(14)=F    #
atst->ldatrmon(15)=F    #
flt->ifxtrim=0          # TRIM TYPE
#
flt->rzcudot=   0.000   # udot (ft/sec**2)     0.028
flt->rzcvdot=   0.000   # vdot (ft/sec**2)     0.052
flt->rzcwdot=   0.000   # wdot (ft/sec**2)   -32.257
flt->rzcpdot=   0.000   # pdot (rad/sec**2)    0.000
flt->rzcqdot=   0.000   # qdot (rad/sec**2)    0.000
flt->rzcrdot=   0.000   # rdot (rad/sec**2)    0.000
flt->rzcpds =   0.000   # p (deg/sec)         -0.075
flt->rzcqds =   0.000   # q (deg/sec)          0.020
flt->rzcrds =   0.000   # r (deg/sec)         -0.011
atst->fdemnw =  1.124   # Dem NW Angle    1.124
#
atst->lzsslip=F         # STEADY SIDSLIP TRIM
flt->lfxstabt=F
atst->lfxailt=F
atst->lfxrudt=F
#
#  DRIVER
#  ------
#
atst->rcxlonsk=-0.***********/(6.3)
#
atst->lzaoahld=F        #  AP AOA HOLD FLAG
atst->lzaoacf=F         #  AOA HOLD WITH CL SHIFT
atst->lzpithld=F        #  AP PITCH HOLD FLAG
atst->lzpitalthld=F     #  AP PITCH HOLD WITH ALTITUDE BIAS FLAG
atst->lzpitiashld=F     #  AP PITCH HOLD WITH IAS BIAS FLAG
atst->lzpitrochld=F     #  AP PITCH HOLD WITH ROC BIAS FLAG
atst->lzpassist=F       #  PITCH DRIVE ASSIST FLAG
atst->lzpitcf=F         #  PITCH HOLD WITH CM SHIFT
atst->lzcradalt=F       #  CMD ALT IS RAD ALT
atst->lzrochld=F        #  AP ROC HOLD FLAG
atst->lzrolhld=F        #  AP ROLL HOLD FLAG
atst->lzrassist=F       #  ROLL DRIVE ASSIST FLAG
atst->lzrolcf=F         #  ROLL HOLD WITH CR SHIFT
atst->lzbethld=F        #  AP BETA HOLD FLAG
atst->lzyassist=F       #  YAW DRIVE ASSIST FLAG
atst->lzbetcf=F         #  BETA HOLD WITH YAW MOMENT
atst->lzhdghld=F        #  AP HDG HOLD WITH RUD FLAG
atst->lzhdgcf=F         #  HEADING HOLD WITH CN SHIFT
atst->lzrwyhld=F        #  RUNWAY HEADING HOLD
atst->lzrwycf=F         #  RUNWAY HOLD WITH YAW MOMENT
atst->lzgamhld=F        #  GAMMA HOLD
atst->lznycf=F          #  NY HOLD WITH YAW MOMENT
atst->lziashld=F        #  AP IAS HOLD FLAG
atst->lziasphd=F        #  IAS HOLD VIA PITCH A/P FLAG
atst->lziascf=F         #  IAS HOLD WITH CD SHIFT
atst->lzmachld=F        #  AP MACH HOLD FLAG
atst->lzfnhld=F         #  AP FN HOLD FLAG
atst->lzfnudothld=F     #  FN HLD WITH UDOT BIAS
atst->lzfniashld=F      #  FN HLD WITH IAS BIAS
atst->lzn1hld=F         #  AP N1 HOLD FLAG
flt->lztqhld=F          #  TORQUE HOLD
atst->lztqudothld=F     #  TORQUE HOLD WITH UDOT BIAS
atst->lztassist=F       #  TORQUE HOLD THORTTLE ASSIST
atst->lzeprhld=F        #  AP EPR HOLD FLAG
atst->lzreverse(1)=F    #  REVERSE THRUST FLAG
atst->lzreverse(2)=F    #  REVERSE THRUST FLAG
atst->lzbrknx=F         #  NX BRAKE HOLD
atst->lzbphld=F         #  BRAKE PRESS HOLD WITH PEDAL
atst->lzflare=F         #  FLARE MODE
#
#  BEGIN TEST
#  ----------
#
flt->rdatime=  0.00
flt->rdatend=40.00
atst->ldahisty=T
atst->ldaplot=T
atst->kdarate=1
#
atst->ldaprint=T
WAIT 1
flt->lfxfastm=T
# flt->rfxpitcm=   2.33   # theta (deg)
# flt->rfxrolcm=  -0.34   # phi (deg)
# flt->rfxhdgcm= 318.82+   0.00   # hdg (deg)
#
WAIT 1
#
flt->dmd_cg_in=198.633     # Center of Gravity (in)
flt->fsetcg=T
flt->fsetgw=T
###P '$AUTO/atg/trim_start.pag'
flt->ldatston=T
flt->frz=F
#
###WAIT flt->ldatrimd T
WAIT atst->ldadoprt F
#
#  TEST COMPLETE
#  -------------
#
WAIT atst->ldatstip F
WAIT (atst->kdaiocmd == 0)
#
p '$AUTO/atg/tst_end.pag'
x
