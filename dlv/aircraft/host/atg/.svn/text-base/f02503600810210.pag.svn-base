################################################################################
* %OPLICENSE%                                                                  *
*             RESTRICTED PROPRIETARY SOFTWARE AND DATA                         *
*                                                                              *
* This software and the data incorporated herein is licensed, not sold,        *
* and is protected by Federal and State copyright and related intellectual     *
* property laws. It may not be disclosed, copied, reversed engineered or used  *
* in any manner except in strict accordance with the terms and conditions of   *
* a certain License granted by OPINICUS to FLYRIGHT HOLDINGS INC. and only on  *
* the one King Air B350/200 simulator to which it relates. Sale, lease or      *
* other transfer of the simulator does not authorize the transferee to use     *
* this software and the data incorporated herein unless strict compliance with *
* the terms of the License referenced above has been met. A copy of the        *
* License is available from OPINICUS upon request. Third party licensed        *
* proprietary data may also be incorporated herein and is subject to the       *
* terms of those licenses.                                                     *
* %OPLICENSE%                                                                  * 
################################################################################
#
#  TITLE:       Crosswind Landing
#  -----
#
#  PROJECT:     BEECHCRAFT KING AIR 350
#  -------
#
#  AIRCRAFT:    B350
#  --------
#
#  CRITERIA:    2.e.3
#  --------
#
#  TEST NUMBER: f02503600810210
#  -----------
#
##############################################
#
#  PRELIMINARY
#  -----------
#
# FAA:               2.e.3
# Maneuver:          Crosswind Landing
# File No:           k4701008_cmb.csv
# Flight Test Date:  02/06/01
# Start FT_Time:     0.00
# Stop  FT_Time:     87.98
# Start OP_Time:     5.00
# Stop  OP_Time:     60.00
# -------------------------------------
# Flap:              35.00
# Gear:              1.00
# Weight:            11065.9
# Hp:                847.2
# Kcas:              113.54
# Mach:              0.1736
# TAT:               -5.419
#
WAIT 1
C atst->kdatstno 2503600810210
#
strcpy atst->project   'Beechcraft B350 Training Device - v.'
strcpy atst->aircraft  'B350'
strcpy atst->tst_title 'Crosswind Landing'
strcpy atst->faa_tstno '2.e.3'
strcpy atst->eng_type  'PT6A-60A'
strcpy atst->ft_file   'k4701008_cmb.csv'
#
atst->ft_time_hack[0]=5.0
atst->ft_time_hack[1]=60.0
#
#
WAIT 1
#
# Include Flight Test Trim Values
# -------------------------------
p '$AUTO/atg/ft_f02503600810210.pag'
#
WAIT RESET
WAIT DEFAULT 1000
AUTOCHANGE NO
WAIT 1
WAIT sim->config_inprogress F
flt->frz=T
# flt->fmwtcg=T
flt->nposfrz=T
flt->ftgactiv=T
atst->lixindis=T
#
#  PARAMETER PLOT
#  --------------
#
PLOT BEGIN
PLOT POINTS
PLOT TITLE 'Crosswind Landing'
PLOT FILE 'f02503600810210.PLT'
P '$AUTO/plot/f02503600810210.plt'
PLOT END
#
#  LFI TABLES
#  ----------
#
LFI RESET
P '$AUTO/lfi_atg/f02503600810210.lfi'
#
##############################################
#
#  WEIGHT AND BALANCE
#  ------------------
#
# Total Fuel Load divided evenly between left and right
fuel->fdemtnk[0]= 464.5  # Left Total  = 449.8 (Main=449.8,Aux=  0.0)
fuel->fdemtnk[1]= 464.5  # Right Total = 479.2 (Main=479.2,Aux=  0.0)
fuel->fsetfuel=T
flt->fdemgw=     11065.9  # Gross Weight (lbs)
flt->fsetgw=T
wait 20 flt->fwgrss 10865.9 11265.9
flt->dmd_cg_in=196.491     # Center of Gravity (in)
flt->fsetcg=T
flt->fsetgw=T
flt->fdemixx=     18275.5  # Ixx (slug-ft^2)
flt->fdemiyy=     17760.9  # Iyy (slug-ft^2)
flt->fdemizz=     32710.2  # Izz (slug-ft^2)
flt->fdemixz=      1815.0  # Ixz (slug-ft^2)
# flt->fsetiner=T            # Set Inertias
#
#  ENVIRONMENT
#  -----------
#
flt->fdembaro=  29.92  # barometric pressure
WAIT 1
flt->fsetbaro=T
flt->fdemtemp=  -6.87  # oat (deg C)
WAIT 1
flt->fsetoat=T
flt->fdemwdir=229 #195 #210 #202.92 #273.8 #270.0  #    0.00 # Wind Direction
flt->fdemwsp= 5.0 #9.30 #9.71 #11.2 #10.0 # 7.00  #    0.00 # Wind Speed
flt->fsetwdir=T
flt->fsetwsp=T
flt->fdemrwc=0         # runway conditions, dry
flt->fsetrwc=T
#
#  FLIGHT CONDITIONS
#  -----------------
#
flt->fdemhter=0.0      # 0.0 field elevation (ft)
flt->fsethter=T
flt->fdemalt=  847.2    # alt (ft)
flt->fsetalt=1
wait 20 flt->fhp   797.2   897.2
flt->fdemhter=530.0-1.0      # 0.0 field elevation (ft)
flt->fsethter=T
atst->rzcalthp=  847.2   # demanded alt (ft)
#
flt->rtcvc=    113.54-0.9   # vc (kts)
flt->rzcroc=   -909.35+245.0+40.0   # roc (fpm)
flt->rfxpitcm=   -2.36   # theta (deg)
flt->rfxrolcm=   -2.63   # phi (deg)
flt->rfxhdgcm= 311.81+flt->magvar   # hdg (deg)
#
#  AIRCRAFT CONFIGURATION
#  ----------------------
#
ctl->fsetflap=T
ctl->fdemflap=   2.00  # flap (deg)
ctl->fsetgear=T
ctl->fdemgear=   1.00  # gear
wait 20 ctl->gear.avg_pos  0.90  1.10
wait 20 ctl->flap.avg_surf_pos 34.50 35.50 # (deg)
#
flt->fdemstab=   6.2 # 7.237   #   7.237 longitudinal trim pos.
atst->fdemrtab=  0.5 #-1.613   #  -1.613 rudder trim pos.
atst->fdematab= -5.0 #  0.334   #   0.334 aileron trim pos.
atst->fsetstab=T
atst->fsetrtab=T
atst->fsetatab=T
atst->fsetydmp=F  # Yaw Damper
#
#  ENGINES
#  -------
#
flt->rzcn1[0]=      76.427  # Engine 1 N1 (perc)
flt->rzcn1[1]=      76.528  # Engine 2 N1 (perc)
atst->rzcn2[0]=   1552.960  # Engine 1 N2 (perc)
atst->rzcn2[1]=   1546.857  # Engine 2 N2 (perc)
eng->dmd_prla[0]=  30.0 #44.28   #    43.72 Engine 1 PROPELLER LEVER ANGLE (deg)
eng->dmd_prla[1]=  30.0 #44.28   #    44.84 Engine 2 PROPELLER LEVER ANGLE (deg)
eng->dmd_cl[0]=     20.02   #    19.60 Engine 1 CONDITION LEVER ANGLE (deg)
eng->dmd_cl[1]=     20.02   #    20.44 Engine 2 CONDITION LEVER ANGLE (deg)
eng->fdemeng[0]=    44.08   #    43.11 Engine 1 TLA (deg)
eng->fdemeng[1]=    44.08   #    45.06 Engine 2 TLA (deg)
atst->rzctq[0]=    292.69   #   279.66 Engine 1 Torque (ft-lb)
atst->rzctq[1]=    292.69   #   305.73 Engine 2 Torque (ft-lb)
wait 1
#
#  SYSTEMS
#  -------
#
hyd.In.di_park_brake=F
atst->fdemtoeb=   0.21  # Cmd Brake Pos
atst->fdemtoer=   0.23  # Cmd Brake Pos
atst->fsettoeb=T
atst->fsettoer=T
atst->rzcbp[0]=   0.00  # Cmd Brk Press
atst->rzcbp[1]=   0.00  # Cmd Brk Press
#
##############################################
#
#  TRIMMER
#  -------
#
atst->kaxpmode=4        # PRIMARY CONTROLS PITCH MODE
atst->kaxrmode=4        # PRIMARY CONTROLS ROLL MODE
atst->kaxymode=4        # PRIMARY CONTROLS YAW MODE
atst->rdtrtime=30.00    # TIME WAIT ELAPSED
atst->ldatrmon(1)=T     # TRIM TIMER
atst->ldatrmon(2)=F     #
atst->ldatrmon(3)=F     #
atst->ldatrmon(4)=T     # ROC
atst->ldatrmon(5)=F     #
atst->ldatrmon(6)=T     # TRANSLATIONAL ACCEL
atst->ldatrmon(7)=T     # ROTATIONAL ACCEL
atst->ldatrmon(8)=F     # ROTATIONAL RATES, 0 IF COMMANDING BANK ANGLE
atst->ldatrmon(9)=F     # LONGITUDINAL CONTROL FORCE
atst->ldatrmon(10)=F    #
atst->ldatrmon(11)=F    #
atst->ldatrmon(12)=T    # GROSS WEIGHT
atst->ldatrmon(13)=T    # x CG
atst->ldatrmon(14)=F    #
atst->ldatrmon(15)=F    #
flt->ifxtrim=3          # TRIM TYPE
#
atst->rzdgain[8]=0.5
atst->rzpgain[8]=1.0
atst->rzpgain[0]=0.15
atst->rzpgain[9] = 0.05
#
flt->rzcudot=  -1.600   # udot (ft/sec**2)    -1.949
flt->rzcvdot=  -1.100   # vdot (ft/sec**2)    -3.000
flt->rzcwdot=   0.000   # wdot (ft/sec**2)   -31.925
flt->rzcpdot=   0.000   # pdot (rad/sec**2)    0.000
flt->rzcqdot=   0.000   # qdot (rad/sec**2)    0.000
flt->rzcrdot=   0.000   # rdot (rad/sec**2)    0.000
flt->rzcpds =   0.000   # p (deg/sec)         -0.104
flt->rzcqds =   0.000   # q (deg/sec)          0.669
flt->rzcrds =   0.000   # r (deg/sec)         -1.397
#
atst->lzsslip=F         # STEADY SIDSLIP TRIM
flt->lfxstabt=F
atst->lfxailt=F
atst->lfxrudt=F
#
#  DRIVER
#  ------
#
#
atst->lzaoahld=F        #  AP AOA HOLD FLAG
atst->lzaoacf=F         #  AOA HOLD WITH CL SHIFT
atst->lzpithld=T        #  AP PITCH HOLD FLAG
atst->lzpitalthld=T     #  AP PITCH HOLD WITH ALTITUDE BIAS FLAG
atst->lzpitiashld=F     #  AP PITCH HOLD WITH IAS BIAS FLAG
atst->lzpitrochld=F     #  AP PITCH HOLD WITH ROC BIAS FLAG
atst->lzpassist=F       #  PITCH DRIVE ASSIST FLAG
atst->lzpitcf=F         #  PITCH HOLD WITH CM SHIFT
atst->lzcradalt=F       #  CMD ALT IS RAD ALT
atst->lzrochld=F        #  AP ROC HOLD FLAG
atst->lzrolhld=T        #  AP ROLL HOLD FLAG
atst->lzrassist=T       #  ROLL DRIVE ASSIST FLAG
atst->lzrolcf=F         #  ROLL HOLD WITH CR SHIFT
atst->lzbethld=F        #  AP BETA HOLD FLAG
atst->lzyassist=F       #  YAW DRIVE ASSIST FLAG
atst->lzbetcf=F         #  BETA HOLD WITH YAW MOMENT
atst->lzhdghld=T        #  AP HDG HOLD WITH RUD FLAG
atst->lzhdgcf=F         #  HEADING HOLD WITH CN SHIFT
atst->lzrwyhld=F        #  RUNWAY HEADING HOLD
atst->lzrwycf=F         #  RUNWAY HOLD WITH YAW MOMENT
atst->lzgamhld=F        #  GAMMA HOLD
atst->lznycf=F          #  NY HOLD WITH YAW MOMENT
atst->lziashld=F        #  AP IAS HOLD FLAG
atst->lziasphd=F        #  IAS HOLD VIA PITCH A/P FLAG
atst->lziascf=F         #  IAS HOLD WITH CD SHIFT
atst->lzmachld=F        #  AP MACH HOLD FLAG
atst->lzfnhld=F         #  AP FN HOLD FLAG
atst->lzfnudothld=F     #  FN HLD WITH UDOT BIAS
atst->lzfniashld=F      #  FN HLD WITH IAS BIAS
atst->lzn1hld=F         #  AP N1 HOLD FLAG
flt->lztqhld=F          #  TORQUE HOLD
atst->lztqudothld=F     #  TORQUE HOLD WITH UDOT BIAS
atst->lztassist=F       #  TORQUE HOLD THORTTLE ASSIST
atst->lzeprhld=F        #  AP EPR HOLD FLAG
atst->lzreverse(1)=F    #  REVERSE THRUST FLAG
atst->lzreverse(2)=F    #  REVERSE THRUST FLAG
atst->lzbrknx=F         #  NX BRAKE HOLD
atst->lzbphld=F         #  BRAKE PRESS HOLD WITH PEDAL
atst->lzflare=T         #  FLARE MODE
atst->rzflareh=50.0
#
flt->frwyhdg=319.7
#
#  BEGIN TEST
#  ----------
#
flt->rdatime=  0.00
flt->rdatend= 55.00
atst->ldahisty=T
atst->ldaplot=T
atst->kdarate=1
#
atst->ldaprint=T
WAIT 1
flt->lfxfastm=T
flt->rfxpitcm=  -2.36   # theta (deg)
flt->rfxrolcm=  -2.63   # phi (deg)
flt->rfxhdgcm= 311.81+flt->magvar  # hdg (deg)
#
WAIT 1
#
flt->dmd_cg_in=196.491     # Center of Gravity (in)
flt->fsetcg=T
flt->fsetgw=T
P '$AUTO/atg/trim_start.pag'
atst->rdatrmlm(6)=0.1
atst->rztqpgain[:] = 0.015
atst->rztqpgain[:] = 0.030
flt->ldatston=T
flt->frz=F
#
WAIT flt->ldatrimd T
WAIT atst->ldadoprt F
#
#  TEST COMPLETE
#  -------------
#
WAIT atst->ldatstip F
WAIT (atst->kdaiocmd == 0)
#
p '$AUTO/atg/tst_end.pag'
x
