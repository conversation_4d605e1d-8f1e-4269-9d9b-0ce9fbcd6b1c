
################################################################################
* %OPLICENSE%                                                                  *
*             RESTRICTED PROPRIETARY SOFTWARE AND DATA                         *
*                                                                              *
* This software and the data incorporated herein is licensed, not sold,        *
* and is protected by Federal and State copyright and related intellectual     *
* property laws. It may not be disclosed, copied, reversed engineered or used  *
* in any manner except in strict accordance with the terms and conditions of   *
* a certain License granted by OPINICUS to FLYRIGHT HOLDINGS INC. and only on  *
* the one King Air B350/200 simulator to which it relates. Sale, lease or      *
* other transfer of the simulator does not authorize the transferee to use     *
* this software and the data incorporated herein unless strict compliance with *
* the terms of the License referenced above has been met. A copy of the        *
* License is available from OPINICUS upon request. Third party licensed        *
* proprietary data may also be incorporated herein and is subject to the       *
* terms of those licenses.                                                     *
* %OPLICENSE%                                                                  * 
################################################################################
#
#   TITLE:     Visual Ground Segment (VGS)
#   ------
# 
#   PROJECT:   BEECHCRAFT KING AIR 350
#   -------
# 
#   AIRCRAFT:  B350
#   --------
# 
#   CRITERIA:  4.i
#   --------
# 
#   TEST NUMBER: f04901000000000
#   -----------
# 
# *********************************************
# 
#   PRELIMINARY
#   -----------
# 
#  FAA:         4.i
#  Manuever:    VGS
#
vis->vgs=1
WAIT 2
C atst->kdatstno 4901000000000
#
strcpy atst->project   'Beechcraft B350 Training Device - v.'
strcpy atst->aircraft  'B350'
strcpy atst->tst_title 'Visual Ground Segment'
strcpy atst->faa_tstno '4.i.1.a'
strcpy atst->eng_type  'PT6A-60A'
strcpy atst->ft_file   'N/A'
#
WAIT 2
WAIT RESET
WAIT DEFAULT 1000
AUTOCHANGE NO
WAIT 1
WAIT sim->config_inprogress F
flt->frz=T
flt->nposfrz=T
flt->ftgactiv=T
atst->lixindis=T
# 
# B350:
# Only run this test if KJFK 04R is the active airport
# /jas if(vis->airport_sel_num==8 && vis->rwy_sel_num==2) {
##################################
# Visual Setup #
#------------------------
# TOD to NIGHT #
vis->io.vvistod=4
#
#--# GROUND FOG SETUP #--#
# NORMAL FOG ON #
vis->ios_fog=1
vis->ugfogd=1
vis->upfogd=0
# FOG HEIGHT > 100.0 ft AGL#
vis->uvishid=300.0
# RVR = 1200.0 ft #
vis->urvrd=1200.0
#
WAIT 2
#
# ALL RUNWAY LIGHTS ON BRIGHT #
vis->io.uappr =5
vis->io.ustrob=5
vis->io.uedge =5
vis->io.ucentr=5
vis->io.utchdw=0
vis->all_lights_set=1
#
WAIT 2
#
# VISIBILITY > 1200.0 ft #
vis->uvismd=100.0 #(1200.0/5280.0)
#
#--# LOWER CLOUD SETUP #--#
# CLOUD LAYER TYPE = OPAQUE #
vis->ucldend=4
# CLOUD LAYER CEILING = 125.0 ft #
vis->uceild=150.0
# CLOUD LAYER TOP = 3000.0 ft #
vis->ucltopd=3480.0
#
#--# UPPER CLOUD SETUP #--#
# UPPER CLOUD LAYER = OFF #
vis->ucldenud=-1
# Runway Contaminant = Dry #
vis->io.rwy_contam_type=0
##################################
# 
#   WEIGHT AND BALANCE
#   ------------------
# 
fuel->fdemtnk[0]=   1559.6 # 2000.0  # Main Tank 1 (lbs)
fuel->fdemtnk[1]=   1559.6 # 2000.0  # Main Tank 2 (lbs)
fuel->fsetfuel=T
flt->fdemgw= 14540 #     13000.0  # Gross Weight (lbs)
flt->fsetgw=T
wait 20 flt->fwgrss  14450 15000
flt->dmd_cg_in=203.0 # 25.682     # Center of Gravity (Percent MAC)
flt->fsetcg=T
flt->fsetgw=T
# 
#   FLIGHT CONDITIONS
#   -----------------
# 
flt->frz= 1

# These values will give a Radio Alt of 100 feet
# C NGLAT  49.2000018615   #49.2002560429   #49.2000526595   #49.2000880353   #49.2005345116
# C NGLON  -123.1553315219 #-123.1574583126 #-123.1557558945 #-123.1561968416 #-123.1556010771
#
# This value will put the main gear at 100 feet AGL
#geo->init_lat=  37.6124133522 #37.6124347249 #37.6126395690
#geo->init_lon=  -122.3544956753 # -122.3545475302 #-122.3549792016
#geo->init_quat = 1
#
#vis->veyelat                       37.6124315357
#vis->veyelon                     -122.3545380891
#vis->veyealt                          117.951149
#
#####################################
#KJFK 04R Freq: 109.50
pl21->switchio_host.nav1_frequency=109500
#
flt->ftorst=1
#flt->ftotmr=0.0
geo->init_lat=40.625473
geo->init_lon=-73.770348
geo->init_quat=1
flt->rfxhdgcm=30.631001
wait 5
flt->ftorst=1
wait 60 vis->dbloaded eq 1

flt->fhggnd=500.0
#flt->fuea=220
geo->init_lat=   40.6229604396 #  40.6229189246
geo->init_lon=  -73.7722527354 # -73.7722853282
geo->init_quat = 1
#flt->fhp=109.0
flt->fdemalt=  119.0    # alt (ft)
flt->fsetalt=1
wait 20 flt->fhp    100.0   130.0
WAIT 1
#####################################
#
#C FHP 114
if (!sim->online) {
# C nav->vhf1rcp.pwr_sw 1
# C nav->vhf1rxd.cb_pwr 1
# C nav->vhf1rcp.freqx 111950000
# C XN1NAVFQ 111950000

 flt->fdemhter=14.0
 flt->fsethter=1
 flt->fhter=14.0
}
WAIT 1
# 
#FHGGND= 101.0 #102.0    # alt (ft)
# FDEMALT= FHTER+ 101.0 #102.0   # alt (ft)

# These values will give a Radio Alt of 100 feet
# FDEMALT=  120.098579 #125.92067   # alt (ft)
# FSETHP=T
# This value will put the main gear at 100 feet AGL
# This is what the TP9685E calls for
#FDEMALT= 115.343506 #115.343506 #114.599998 #105.999931
#FSETHP=T
#
#wait 10
##################################
# 
flt->rtcvc=120.0         # ve (kts)
flt->rzcroc=-645.0 #-497.0 #-620.0       # roc (fpm)
flt->rfxpitcm=-1.00      # theta (deg)
flt->rfxrolcm=0.00       # phi (deg)
flt->rfxhdgcm=30.631001  # hdg (deg)
# 
#   AIRCRAFT CONFIGURATION
#   ----------------------
# 
ctl->fsetflap=T
ctl->fdemflap=  2.00  # flaps (deg)
ctl->fsetgear=T
ctl->fdemgear=   1.00  # gear
wait 20 ctl->gear.avg_pos  0.90 1.00
wait 20 ctl->flap.avg_surf_pos 34.00 36.00 # (deg)
#
flt->fdemstab=    3.426   #  longitudinal trim pos.
atst->fsetstab=T
atst->fdemrtab=   0.000   #  rudder trim pos.
atst->fsetrtab=T
atst->fdematab=   0.000   #  aileron trim pos.
atst->fsetatab=T
atst->fsetydmp=F  # Yaw Damper
# 
#   ENGINES
#   -------
# 
#RZCN1(1)=   56.938  # Engine 1 N1 (perc)
#RZCN1(2)=   57.469  # Engine 2 N1 (perc)
#RZCN2(1)=   80.406  # Engine 1 N2 (perc)
#RZCN2(2)=   80.609  # Engine 2 N2 (perc)
#FDEMENG(1)= -10.40  # Engine 1 TLA (deg)
#FDEMENG(2)= -10.45  # Engine 2 TLA (deg)
eng->dmd_prla[0]= 45.0    # Engine 1 PROPELLER LEVER ANGLE (deg)
eng->dmd_prla[1]= 45.0    # Engine 2 PROPELLER LEVER ANGLE (deg)
eng->dmd_cl[0]=   20.0 # Engine 1 CONDITION LEVER ANGLE (deg)
eng->dmd_cl[1]=   20.0 # Engine 2 CONDITION LEVER ANGLE (deg)
eng->fdemeng[0]=  46.0   # Engine 1 TLA (deg)
eng->fdemeng[1]=  46.0   # Engine 2 TLA (deg)
wait 1
# 
#   ENVIRONMENT
#   -----------
# 
flt->fdembaro= 29.92  #  Barometric Pressure (in. Hg)
WAIT 1
flt->fsetbaro=T       #  Set Barometric Pressure
flt->fdemtemp=  15.0  #  OAT (deg C)
WAIT 1
flt->fsetoat=T        #  Set OAT
flt->fdemwdir= 0.00   #  Wind Direction
flt->fsetwdir=T       #  Set Wind Direction
flt->fdemwsp=  0.00   #  Wind Speed (knots)
flt->fsetwsp=T        #  Set Wind Speed
flt->fdemrwc=0        #  Runway Conditions, Dry
flt->fsetrwc=T        #  Set Runway Conditions
# 
#   SYSTEMS
#   -------
#
hyd.In.di_park_brake=F
atst->fdemtoeb=  0.0  # Cmd Brake Pos
atst->fdemtoer=  0.0  # Cmd Brake Pos
atst->fsettoeb=T
atst->fsettoer=T
# 
# *********************************************
# 
#   TRIMMER
#   -------
# 
wait 3
atst->kaxpmode=4        # PRIMARY CONTROLS PITCH MODE
atst->kaxrmode=4        # PRIMARY CONTROLS ROLL MODE
atst->kaxymode=4        # PRIMARY CONTROLS YAW MODE
atst->rdtrtime=1000.00  # TIME WAIT ELAPSED
atst->ldatrmon(1)=T     # TRIM TIMER
atst->ldatrmon(2)=F     #
atst->ldatrmon(3)=F     #
atst->ldatrmon(4)=T     # ROC
atst->ldatrmon(5)=F     #
atst->ldatrmon(6)=T     # TRANSLATIONAL ACCEL
atst->ldatrmon(7)=T     # ROTATIONAL ACCEL
atst->ldatrmon(8)=F     # ROTATIONAL RATES, 0 IF COMMANDING BANK ANGLE
atst->ldatrmon(9)=F     # LONGITUDINAL CONTROL FORCE
atst->ldatrmon(10)=F    #
atst->ldatrmon(11)=F    #
atst->ldatrmon(12)=T    # GROSS WEIGHT
atst->ldatrmon(13)=T    # x CG
atst->ldatrmon(14)=F    #
atst->ldatrmon(15)=F    #
flt->ifxtrim=3          # TRIM TYPE
#
flt->rzcudot=   0.000   # udot (ft/sec**2)
flt->rzcvdot=   0.000   # vdot (ft/sec**2)
flt->rzcwdot=   0.000   # wdot (ft/sec**2)
flt->rzcpdot=   0.000   # pdot (rad/sec**2)
flt->rzcqdot=   0.000   # qdot (rad/sec**2)
flt->rzcrdot=   0.000   # rdot (rad/sec**2)
flt->rzcpds =   0.000   # p (deg/sec)
flt->rzcqds =   0.000   # q (deg/sec)
flt->rzcrds =   0.000   # r (deg/sec)
atst->fdemnw =  0.000   # Dem NW Angle
#
atst->lzsslip=F         # STEADY SIDSLIP TRIM
flt->lfxstabt=T
atst->lfxailt=F
atst->lfxrudt=F
# 
#   DRIVER
#   ------
# 
atst->lzaoahld=F        #  AP AOA HOLD FLAG
atst->lzaoacf=F         #  AOA HOLD WITH CL SHIFT
atst->lzpithld=F        #  AP PITCH HOLD FLAG
atst->lzpitalthld=F     #  AP PITCH HOLD WITH ALTITUDE BIAS FLAG
atst->lzpitiashld=F     #  AP PITCH HOLD WITH IAS BIAS FLAG
atst->lzpitrochld=F     #  AP PITCH HOLD WITH ROC BIAS FLAG
atst->lzpassist=F       #  PITCH DRIVE ASSIST FLAG
atst->lzpitcf=F         #  PITCH HOLD WITH CM SHIFT
atst->lzcradalt=F       #  CMD ALT IS RAD ALT
atst->lzrochld=F        #  AP ROC HOLD FLAG
atst->lzrolhld=F        #  AP ROLL HOLD FLAG
atst->lzrassist=F       #  ROLL DRIVE ASSIST FLAG
atst->lzrolcf=F         #  ROLL HOLD WITH CR SHIFT
atst->lzbethld=F        #  AP BETA HOLD FLAG
atst->lzyassist=F       #  YAW DRIVE ASSIST FLAG
atst->lzbetcf=F         #  BETA HOLD WITH YAW MOMENT
atst->lzhdghld=F        #  AP HDG HOLD WITH RUD FLAG
atst->lzhdgcf=F         #  HEADING HOLD WITH CN SHIFT
atst->lzrwyhld=F        #  RUNWAY HEADING HOLD
atst->lzrwycf=F         #  RUNWAY HOLD WITH YAW MOMENT
atst->lzgamhld=F        #  GAMMA HOLD
atst->lznycf=F          #  NY HOLD WITH YAW MOMENT
atst->lziashld=F        #  AP IAS HOLD FLAG
atst->lziasphd=F        #  IAS HOLD VIA PITCH A/P FLAG
atst->lziascf=F         #  IAS HOLD WITH CD SHIFT
atst->lzmachld=F        #  AP MACH HOLD FLAG
atst->lzfnhld=F         #  AP FN HOLD FLAG
atst->lzfnudothld=F     #  FN HLD WITH UDOT BIAS
atst->lzfniashld=F      #  FN HLD WITH IAS BIAS
atst->lzn1hld=F         #  AP N1 HOLD FLAG
flt->lztqhld=F          #  TORQUE HOLD
atst->lztqudothld=F     #  TORQUE HOLD WITH UDOT BIAS
atst->lztassist=F       #  TORQUE HOLD THORTTLE ASSIST
atst->lzeprhld=F        #  AP EPR HOLD FLAG
atst->lzreverse(1)=F    #  REVERSE THRUST FLAG
atst->lzreverse(2)=F    #  REVERSE THRUST FLAG
atst->lzbrknx=F         #  NX BRAKE HOLD
atst->lzbphld=F         #  BRAKE PRESS HOLD WITH PEDAL
atst->lzflare=F         #  FLARE MODE
#LZLIAS=F          #  COMMAND V IS IAS

#
#  BEGIN TEST
#  ----------
#
# C FHWELEV T    # HINGEWISE ELEVATOR INPUTS FLG
# C FHWATAB T    # HINGEWISE AIL. TAB INPUTS FLG
# C FHWRUD  T    # HINGEWISE RUDDER INPUT FLAG
#
flt->rdatime=    0.00
flt->rdatend= 1000.00
atst->ldahisty=T
atst->ldaplot=T
atst->kdarate=1
#
atst->ldaprint=T
WAIT 1
flt->lfxfastm=T
flt->rfxpitcm=  -1.00      # theta (deg)
flt->rfxrolcm=   0.00      # phi (deg)
flt->rfxhdgcm=  30.631001  # hdg (deg)
#
WAIT 1
#
flt->dmd_cg_in=203.0 #199.4 # 25.682     # Center of Gravity (Percent MAC)
flt->fsetcg=T
flt->fsetgw=T
P '$AUTO/atg/trim_start.pag'
flt->ldatston=T
flt->frz=T
#
WAIT flt->ldatrimd T
WAIT atst->ldadoprt F
#
#  TEST COMPLETE
#  -------------
#
WAIT atst->ldatstip F
WAIT (atst->kdaiocmd == 0)
#
# C FHWELEV F    # HINGEWISE ELEVATOR INPUTS FLG
# C FHWATAB F    # HINGEWISE AIL. TAB INPUTS FLG
# C FHWRUD  F    # HINGEWISE RUDDER INPUT FLAG
##################################
}
p '$AUTO/atg/tst_end.pag'
x
