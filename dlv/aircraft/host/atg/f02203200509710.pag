################################################################################
* %OPLICENSE%                                                                  *
*             RESTRICTED PROPRIETARY SOFTWARE AND DATA                         *
*                                                                              *
* This software and the data incorporated herein is licensed, not sold,        *
* and is protected by Federal and State copyright and related intellectual     *
* property laws. It may not be disclosed, copied, reversed engineered or used  *
* in any manner except in strict accordance with the terms and conditions of   *
* a certain License granted by OPINICUS to FLYRIGHT HOLDINGS INC. and only on  *
* the one King Air B350/200 simulator to which it relates. Sale, lease or      *
* other transfer of the simulator does not authorize the transferee to use     *
* this software and the data incorporated herein unless strict compliance with *
* the terms of the License referenced above has been met. A copy of the        *
* License is available from OPINICUS upon request. Third party licensed        *
* proprietary data may also be incorporated herein and is subject to the       *
* terms of those licenses.                                                     *
* %OPLICENSE%                                                                  * 
################################################################################
#
#  TITLE:       Yaw Control
#  -----
#
#  PROJECT:     BEECHCRAFT KING AIR 350
#  -------
#
#  AIRCRAFT:    B350
#  --------
#
#  CRITERIA:    2.b.3
#  --------
#
#  TEST NUMBER: f02203200509710
#  -----------
#
##############################################
#
#  PRELIMINARY
#  -----------
#
# FAA:               2.b.3
# Maneuver:          Yaw Control
# File No:           k4402005_cmb.csv
# Flight Test Date:  02/01/01
# Start FT_Time:     0.00
# Stop  FT_Time:     243.98
# Start OP_Time:     0.00
# Stop  OP_Time:     243.98
# -------------------------------------
# Flap:              0.00
# Gear:              1.00
# Weight:            12171.7
# Hp:                7978.9
# Kcas:              149.21
# Mach:              0.2594
# TAT:               -12.647
#
WAIT 1
C atst->kdatstno 2203200509710
#
strcpy atst->tst_title 'Yaw Control'
#
WAIT 1
#
# Include Flight Test Trim Values
# -------------------------------
p '$AUTO/atg/ft_f02203200509710.pag'
#
WAIT RESET
WAIT DEFAULT 1000
AUTOCHANGE NO
WAIT 1
WAIT sim->config_inprogress F
flt->frz=T
# flt->fmwtcg=T
flt->nposfrz=T
flt->ftgactiv=T
atst->lixindis=T
#
#  PARAMETER PLOT
#  --------------
#
PLOT BEGIN
PLOT POINTS
PLOT TITLE 'Yaw Control'
PLOT FILE 'f02203200509710.PLT'
P '$AUTO/plot/f02203200509710.plt'
PLOT END
#
#  LFI TABLES
#  ----------
#
LFI RESET
P '$AUTO/lfi_atg/f02203200509710.lfi'
#
##############################################
#
#  WEIGHT AND BALANCE
#  ------------------
#
# Total Fuel Load divided evenly between left and right
fuel->fdemtnk[0]= 929.8  # Left Total  = 1001.4 (Main=1001.4,Aux=  0.0)
fuel->fdemtnk[1]= 929.8  # Right Total = 858.3 (Main=858.3,Aux=  0.0)
fuel->fsetfuel=T
flt->fdemgw=     12171.7  # Gross Weight (lbs)
flt->fsetgw=T
wait 20 flt->fwgrss 11971.7 12371.7
flt->dmd_cg_in=197.660     # Center of Gravity (in)
flt->fsetcg=T
flt->fsetgw=T
flt->fdemixx=     23965.5  # Ixx (slug-ft^2)
flt->fdemiyy=     17922.0  # Iyy (slug-ft^2)
flt->fdemizz=     38540.8  # Izz (slug-ft^2)
flt->fdemixz=      1847.6  # Ixz (slug-ft^2)
# flt->fsetiner=T            # Set Inertias
#
#  ENVIRONMENT
#  -----------
#
flt->fdembaro=  29.92  # barometric pressure
WAIT 1
flt->fsetbaro=T
flt->fdemtemp= -15.96  # oat (deg C)
WAIT 1
flt->fsetoat=T
flt->fdemwdir=   0.00  #    0.00 # Wind Direction
flt->fdemwsp=    0.00  #    0.00 # Wind Speed
flt->fsetwdir=T
flt->fsetwsp=T
flt->fdemrwc=0         # runway conditions, dry
flt->fsetrwc=T
#
#  FLIGHT CONDITIONS
#  -----------------
#
flt->fdemhter=0.0      # 0.0 field elevation (ft)
flt->fsethter=T
flt->fdemalt= 7978.9    # alt (ft)
flt->fsetalt=1
wait 20 flt->fhp  7928.9  8028.9
flt->fdemhter=0.0      # 0.0 field elevation (ft)
flt->fsethter=T
atst->rzcalthp= 7978.9   # demanded alt (ft)
#
flt->rtcvc=    149.21   # vc (kts)
flt->rzcroc=     80.40   # roc (fpm)
flt->rfxpitcm=    3.39   # theta (deg)
flt->rfxrolcm=   -0.30   # phi (deg)
flt->rfxhdgcm= 296.74+   0.00   # hdg (deg)
#
#  AIRCRAFT CONFIGURATION
#  ----------------------
#
ctl->fsetflap=T
ctl->fdemflap=   0.00  # flap (deg)
ctl->fsetgear=T
ctl->fdemgear=   1.00  # gear
wait 20 ctl->gear.avg_pos  0.90  1.10
wait 20 ctl->flap.avg_surf_pos -0.50 0.50 # (deg)
#
flt->fdemstab=    3.576   #   3.576 longitudinal trim pos.
atst->fdemrtab=  -0.997   #  -0.997 rudder trim pos.
atst->fdematab=   0.292   #   0.292 aileron trim pos.
atst->fsetstab=T
atst->fsetrtab=T
atst->fsetatab=T
atst->fsetydmp=F  # Yaw Damper
#
#  ENGINES
#  -------
#
flt->rzcn1[0]=      86.864  # Engine 1 N1 (perc)
flt->rzcn1[1]=      87.342  # Engine 2 N1 (perc)
atst->rzcn2[0]=   1503.872  # Engine 1 N2 (perc)
atst->rzcn2[1]=   1503.731  # Engine 2 N2 (perc)
eng->dmd_prla[0]=   17.41   #    16.69 Engine 1 PROPELLER LEVER ANGLE (deg)
eng->dmd_prla[1]=   17.41   #    18.14 Engine 2 PROPELLER LEVER ANGLE (deg)
eng->dmd_cl[0]=     20.09   #    19.74 Engine 1 CONDITION LEVER ANGLE (deg)
eng->dmd_cl[1]=     20.09   #    20.44 Engine 2 CONDITION LEVER ANGLE (deg)
eng->fdemeng[0]=    48.67   #    47.75 Engine 1 TLA (deg)
eng->fdemeng[1]=    48.67   #    49.59 Engine 2 TLA (deg)
atst->rzctq[0]=   1483.35   #  1471.21 Engine 1 Torque (ft-lb)
atst->rzctq[1]=   1483.35   #  1495.50 Engine 2 Torque (ft-lb)
eng->ebstart[:]=1           # Faststart both engines
wait 1
#
#  SYSTEMS
#  -------
#
hyd.In.di_park_brake=F
atst->fdemtoeb=   0.12  # Cmd Brake Pos
atst->fdemtoer=   0.59  # Cmd Brake Pos
atst->fsettoeb=F
atst->fsettoer=F
atst->rzcbp[0]=   0.00  # Cmd Brk Press
atst->rzcbp[1]=   0.00  # Cmd Brk Press
#
##############################################
#
#  TRIMMER
#  -------
#
atst->kaxpmode=4        # PRIMARY CONTROLS PITCH MODE
atst->kaxrmode=4        # PRIMARY CONTROLS ROLL MODE
atst->kaxymode=4        # PRIMARY CONTROLS YAW MODE
atst->rdtrtime=30.00    # TIME WAIT ELAPSED
atst->ldatrmon(1)=T     # TRIM TIMER
atst->ldatrmon(2)=F     #
atst->ldatrmon(3)=F     #
atst->ldatrmon(4)=T     # ROC
atst->ldatrmon(5)=F     #
atst->ldatrmon(6)=T     # TRANSLATIONAL ACCEL
atst->ldatrmon(7)=T     # ROTATIONAL ACCEL
atst->ldatrmon(8)=F     # ROTATIONAL RATES, 0 IF COMMANDING BANK ANGLE
atst->ldatrmon(9)=F     # LONGITUDINAL CONTROL FORCE
atst->ldatrmon(10)=F    #
atst->ldatrmon(11)=F    #
atst->ldatrmon(12)=T    # GROSS WEIGHT
atst->ldatrmon(13)=T    # x CG
atst->ldatrmon(14)=F    #
atst->ldatrmon(15)=F    #
flt->ifxtrim=3          # TRIM TYPE
#
flt->rzcudot=   0.000   # udot (ft/sec**2)    -0.405
flt->rzcvdot=   0.000   # vdot (ft/sec**2)    -0.135
flt->rzcwdot=   0.000   # wdot (ft/sec**2)   -32.113
flt->rzcpdot=   0.000   # pdot (rad/sec**2)    0.000
flt->rzcqdot=   0.000   # qdot (rad/sec**2)    0.000
flt->rzcrdot=   0.000   # rdot (rad/sec**2)    0.000
flt->rzcpds =   0.000   # p (deg/sec)         -0.125
flt->rzcqds =   0.000   # q (deg/sec)         -0.019
flt->rzcrds =   0.000   # r (deg/sec)         -0.066
#
atst->lzsslip=F         # STEADY SIDSLIP TRIM
flt->lfxstabt=F
atst->lfxailt=F
atst->lfxrudt=F
#
#  DRIVER
#  ------
#
#
atst->lzaoahld=F        #  AP AOA HOLD FLAG
atst->lzaoacf=F         #  AOA HOLD WITH CL SHIFT
atst->lzpithld=F        #  AP PITCH HOLD FLAG
atst->lzpitalthld=F     #  AP PITCH HOLD WITH ALTITUDE BIAS FLAG
atst->lzpitiashld=F     #  AP PITCH HOLD WITH IAS BIAS FLAG
atst->lzpitrochld=F     #  AP PITCH HOLD WITH ROC BIAS FLAG
atst->lzpassist=F       #  PITCH DRIVE ASSIST FLAG
atst->lzpitcf=F         #  PITCH HOLD WITH CM SHIFT
atst->lzcradalt=F       #  CMD ALT IS RAD ALT
atst->lzrochld=F        #  AP ROC HOLD FLAG
atst->lzrolhld=F        #  AP ROLL HOLD FLAG
atst->lzrassist=F       #  ROLL DRIVE ASSIST FLAG
atst->lzrolcf=F         #  ROLL HOLD WITH CR SHIFT
atst->lzbethld=F        #  AP BETA HOLD FLAG
atst->lzyassist=F       #  YAW DRIVE ASSIST FLAG
atst->lzbetcf=F         #  BETA HOLD WITH YAW MOMENT
atst->lzhdghld=F        #  AP HDG HOLD WITH RUD FLAG
atst->lzhdgcf=F         #  HEADING HOLD WITH CN SHIFT
atst->lzrwyhld=F        #  RUNWAY HEADING HOLD
atst->lzrwycf=F         #  RUNWAY HOLD WITH YAW MOMENT
atst->lzgamhld=F        #  GAMMA HOLD
atst->lznycf=F          #  NY HOLD WITH YAW MOMENT
atst->lziashld=F        #  AP IAS HOLD FLAG
atst->lziasphd=F        #  IAS HOLD VIA PITCH A/P FLAG
atst->lziascf=F         #  IAS HOLD WITH CD SHIFT
atst->lzmachld=F        #  AP MACH HOLD FLAG
atst->lzfnhld=F         #  AP FN HOLD FLAG
atst->lzfnudothld=F     #  FN HLD WITH UDOT BIAS
atst->lzfniashld=F      #  FN HLD WITH IAS BIAS
atst->lzn1hld=F         #  AP N1 HOLD FLAG
flt->lztqhld=F          #  TORQUE HOLD
atst->lztqudothld=F     #  TORQUE HOLD WITH UDOT BIAS
atst->lztassist=F       #  TORQUE HOLD THORTTLE ASSIST
atst->lzeprhld=F        #  AP EPR HOLD FLAG
atst->lzreverse(1)=F    #  REVERSE THRUST FLAG
atst->lzreverse(2)=F    #  REVERSE THRUST FLAG
atst->lzbrknx=F         #  NX BRAKE HOLD
atst->lzbphld=F         #  BRAKE PRESS HOLD WITH PEDAL
atst->lzflare=F         #  FLARE MODE
#
#  BEGIN TEST
#  ----------
#
flt->rdatime=  0.00
flt->rdatend=243.98
atst->ldahisty=T
atst->ldaplot=T
atst->kdarate=1
#
atst->ldaprint=T
WAIT 1
flt->lfxfastm=T
flt->rfxpitcm=   3.39   # theta (deg)
flt->rfxrolcm=  -0.30   # phi (deg)
flt->rfxhdgcm= 296.74+   0.00   # hdg (deg)
#
WAIT 1
#
flt->dmd_cg_in=197.660     # Center of Gravity (in)
flt->fsetcg=T
flt->fsetgw=T
P '$AUTO/atg/trim_start.pag'
flt->ldatston=T
flt->frz=F
#
WAIT flt->ldatrimd T
WAIT atst->ldadoprt F
#
#  TEST COMPLETE
#  -------------
#
WAIT atst->ldatstip F
WAIT (atst->kdaiocmd == 0)
#
p '$AUTO/atg/tst_end.pag'
x
