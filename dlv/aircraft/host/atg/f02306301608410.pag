################################################################################
* %OPLICENSE%                                                                  *
*             RESTRICTED PROPRIETARY SOFTWARE AND DATA                         *
*                                                                              *
* This software and the data incorporated herein is licensed, not sold,        *
* and is protected by Federal and State copyright and related intellectual     *
* property laws. It may not be disclosed, copied, reversed engineered or used  *
* in any manner except in strict accordance with the terms and conditions of   *
* a certain License granted by OPINICUS to FLYRIGHT HOLDINGS INC. and only on  *
* the one King Air B350/200 simulator to which it relates. Sale, lease or      *
* other transfer of the simulator does not authorize the transferee to use     *
* this software and the data incorporated herein unless strict compliance with *
* the terms of the License referenced above has been met. A copy of the        *
* License is available from OPINICUS upon request. Third party licensed        *
* proprietary data may also be incorporated herein and is subject to the       *
* terms of those licenses.                                                     *
* %OPLICENSE%                                                                  * 
################################################################################
#
#  TITLE:       Longitudinal Maneuvering Stability
#  -----
#
#  PROJECT:     BEECHCRAFT KING AIR 350
#  -------
#
#  AIRCRAFT:    B350
#  --------
#
#  CRITERIA:    2.c.6
#  --------
#
#  TEST NUMBER: f02306301608410
#  -----------
#
##############################################
#
#  PRELIMINARY
#  -----------
#
# FAA:               2.c.6
# Maneuver:          Longitudinal Maneuvering Stability
# File No:           k3901016_cmb.csv
# Flight Test Date:  01/28/01
# Start FT_Time:     0.00
# Stop  FT_Time:     183.98
# Start OP_Time:     5.00
# Stop  OP_Time:     125.00
# -------------------------------------
# Flap:              14.00
# Gear:              0.00
# Weight:            14234.6
# Hp:                7569.1
# Kcas:              138.48
# Mach:              0.2392
# TAT:               -18.943
#
WAIT 1
C atst->kdatstno 2306301608410
#
strcpy atst->project   'Beechcraft B350 Training Device - v.'
strcpy atst->aircraft  'B350'
strcpy atst->tst_title 'Longitudinal Maneuvering Stability, Approach'
strcpy atst->faa_tstno '2.c.6.b'
strcpy atst->eng_type  'PT6A-60A'
strcpy atst->ft_file   'k3901016_cmb.csv'
#
atst->ft_time_hack[0]=5.0
atst->ft_time_hack[1]=125.0
#
#
WAIT 1
#
# Include Flight Test Trim Values
# -------------------------------
p '$AUTO/atg/ft_f02306301608410.pag'
#
WAIT RESET
WAIT DEFAULT 1000
AUTOCHANGE NO
WAIT 1
WAIT sim->config_inprogress F
flt->frz=T
# flt->fmwtcg=T
flt->nposfrz=T
flt->ftgactiv=T
atst->lixindis=T
#
#  PARAMETER PLOT
#  --------------
#
PLOT BEGIN
PLOT POINTS
PLOT TITLE 'Longitudinal Maneuvering Stability, Approach'
PLOT FILE 'f02306301608410.PLT'
P '$AUTO/plot/f02306301608410.plt'
PLOT END
#
#  LFI TABLES
#  ----------
#
LFI RESET
P '$AUTO/lfi_atg/f02306301608410.lfi'
#
##############################################
#
#  WEIGHT AND BALANCE
#  ------------------
#
# Total Fuel Load divided evenly between left and right
fuel->fdemtnk[0]= 1411.3  # Left Total  = 1418.1 (Main=1298.8,Aux=119.3)
fuel->fdemtnk[1]= 1411.3  # Right Total = 1404.5 (Main=1285.2,Aux=119.3)
fuel->fsetfuel=T
flt->fdemgw=     14234.6  # Gross Weight (lbs)
flt->fsetgw=T
wait 20 flt->fwgrss 14034.6 14434.6
flt->dmd_cg_in=201.749     # Center of Gravity (in)
flt->fsetcg=T
flt->fsetgw=T
flt->fdemixx=     32448.9  # Ixx (slug-ft^2)
flt->fdemiyy=     20887.7  # Iyy (slug-ft^2)
flt->fdemizz=     49958.7  # Izz (slug-ft^2)
flt->fdemixz=      1795.0  # Ixz (slug-ft^2)
# flt->fsetiner=T            # Set Inertias
#
#  ENVIRONMENT
#  -----------
#
flt->fdembaro=  29.92  # barometric pressure
WAIT 1
flt->fsetbaro=T
flt->fdemtemp= -21.67  # oat (deg C)
WAIT 1
flt->fsetoat=T
flt->fdemwdir=   0.00  #    0.00 # Wind Direction
flt->fdemwsp=    0.00  #    0.00 # Wind Speed
flt->fsetwdir=T
flt->fsetwsp=T
flt->fdemrwc=0         # runway conditions, dry
flt->fsetrwc=T
#
#  FLIGHT CONDITIONS
#  -----------------
#
flt->fdemhter=0.0      # 0.0 field elevation (ft)
flt->fsethter=T
flt->fdemalt= 7569.1    # alt (ft)
flt->fsetalt=1
wait 20 flt->fhp  7519.1  7619.1
flt->fdemhter=0.0      # 0.0 field elevation (ft)
flt->fsethter=T
atst->rzcalthp= 7569.1   # demanded alt (ft)
#
flt->rtcvc=    138.48   # vc (kts)
flt->rzcroc=     60.0    # 10.23   # roc (fpm)
flt->rfxpitcm=    3.16   # theta (deg)
flt->rfxrolcm=    1.10   # phi (deg)
flt->rfxhdgcm=  48.43+   flt->magvar   # hdg (deg)
#
#  AIRCRAFT CONFIGURATION
#  ----------------------
#
ctl->fsetflap=T
ctl->fdemflap=   1.00  # flap (deg)
ctl->fsetgear=T
ctl->fdemgear=   0.00  # gear
wait 20 ctl->gear.avg_pos -0.10  0.10
wait 20 ctl->flap.avg_surf_pos 13.50 14.50 # (deg)
#
flt->fdemstab=    3.467   #   3.467 longitudinal trim pos.
atst->fdemrtab=   0.000   #  -2.046 rudder trim pos.
atst->fdematab=   0.000   #   1.127 aileron trim pos.
atst->fsetstab=T
atst->fsetrtab=T
atst->fsetatab=T
atst->fsetydmp=F  # Yaw Damper
#
#  ENGINES
#  -------
#
flt->rzcn1[0]=      84.820  # Engine 1 N1 (perc)
flt->rzcn1[1]=      84.491  # Engine 2 N1 (perc)
atst->rzcn2[0]=   1600.045  # Engine 1 N2 (perc)
atst->rzcn2[1]=   1607.828  # Engine 2 N2 (perc)
eng->dmd_prla[0]=   28.40   #    27.91 Engine 1 PROPELLER LEVER ANGLE (deg)
eng->dmd_prla[1]=   28.40   #    28.89 Engine 2 PROPELLER LEVER ANGLE (deg)
eng->dmd_cl[0]=     19.96   #    19.55 Engine 1 CONDITION LEVER ANGLE (deg)
eng->dmd_cl[1]=     19.96   #    20.36 Engine 2 CONDITION LEVER ANGLE (deg)
eng->fdemeng[0]=    46.99   #    46.15 Engine 1 TLA (deg)
eng->fdemeng[1]=    46.99   #    47.82 Engine 2 TLA (deg)
atst->rzctq[0]=   1088.96   #  1105.38 Engine 1 Torque (ft-lb)
atst->rzctq[1]=   1088.96   #  1072.54 Engine 2 Torque (ft-lb)
eng->ebstart[:]=1           # Faststart both engines
wait 1
#
#  SYSTEMS
#  -------
#
hyd.In.di_park_brake=F
atst->fdemtoeb=   0.00  # Cmd Brake Pos
atst->fdemtoer=   0.35  # Cmd Brake Pos
atst->fsettoeb=F
atst->fsettoer=F
atst->rzcbp[0]=   0.00  # Cmd Brk Press
atst->rzcbp[1]=   1.44  # Cmd Brk Press
#
##############################################
#
#  TRIMMER
#  -------
#
atst->kaxpmode=4        # PRIMARY CONTROLS PITCH MODE
atst->kaxrmode=4        # PRIMARY CONTROLS ROLL MODE
atst->kaxymode=4        # PRIMARY CONTROLS YAW MODE
atst->rdtrtime=30.00    # TIME WAIT ELAPSED
atst->ldatrmon(1)=T     # TRIM TIMER
atst->ldatrmon(2)=F     #
atst->ldatrmon(3)=F     #
atst->ldatrmon(4)=T     # ROC
atst->ldatrmon(5)=F     #
atst->ldatrmon(6)=T     # TRANSLATIONAL ACCEL
atst->ldatrmon(7)=T     # ROTATIONAL ACCEL
atst->ldatrmon(8)=F     # ROTATIONAL RATES, 0 IF COMMANDING BANK ANGLE
atst->ldatrmon(9)=F     # LONGITUDINAL CONTROL FORCE
atst->ldatrmon(10)=F    #
atst->ldatrmon(11)=F    #
atst->ldatrmon(12)=T    # GROSS WEIGHT
atst->ldatrmon(13)=T    # x CG
atst->ldatrmon(14)=F    #
atst->ldatrmon(15)=F    #
flt->ifxtrim=3          # TRIM TYPE
#
flt->rzcudot=   0.000   # udot (ft/sec**2)    -0.079
flt->rzcvdot=   0.000   # vdot (ft/sec**2)     0.498
flt->rzcwdot=   0.000   # wdot (ft/sec**2)   -32.306
flt->rzcpdot=   0.000   # pdot (rad/sec**2)    0.000
flt->rzcqdot=   0.000   # qdot (rad/sec**2)    0.000
flt->rzcrdot=   0.000   # rdot (rad/sec**2)    0.000
flt->rzcpds =   0.000   # p (deg/sec)         -0.014
flt->rzcqds =   0.000   # q (deg/sec)          0.087
flt->rzcrds =   0.000   # r (deg/sec)          0.033
#
atst->lzsslip=F         # STEADY SIDSLIP TRIM
flt->lfxstabt=F
atst->lfxailt=F
atst->lfxrudt=F
#
#  DRIVER
#  ------
#
#
atst->lzaoahld=F        #  AP AOA HOLD FLAG
atst->lzaoacf=F         #  AOA HOLD WITH CL SHIFT
atst->lzpithld=T        #  AP PITCH HOLD FLAG
atst->lzpitalthld=F     #  AP PITCH HOLD WITH ALTITUDE BIAS FLAG
atst->lzpitiashld=T     #  AP PITCH HOLD WITH IAS BIAS FLAG
atst->lzpitrochld=F     #  AP PITCH HOLD WITH ROC BIAS FLAG
atst->lzpassist=T       #  PITCH DRIVE ASSIST FLAG
atst->lzpitcf=F         #  PITCH HOLD WITH CM SHIFT
atst->lzcradalt=F       #  CMD ALT IS RAD ALT
atst->lzrochld=F        #  AP ROC HOLD FLAG
atst->lzrolhld=T        #  AP ROLL HOLD FLAG
atst->lzrassist=F       #  ROLL DRIVE ASSIST FLAG
atst->lzrolcf=F         #  ROLL HOLD WITH CR SHIFT
atst->lzbethld=F        #  AP BETA HOLD FLAG
atst->lzyassist=F       #  YAW DRIVE ASSIST FLAG
atst->lzbetcf=F         #  BETA HOLD WITH YAW MOMENT
atst->lzhdghld=F        #  AP HDG HOLD WITH RUD FLAG
atst->lzhdgcf=F         #  HEADING HOLD WITH CN SHIFT
atst->lzrwyhld=F        #  RUNWAY HEADING HOLD
atst->lzrwycf=F         #  RUNWAY HOLD WITH YAW MOMENT
atst->lzgamhld=F        #  GAMMA HOLD
atst->lznycf=F          #  NY HOLD WITH YAW MOMENT
atst->lziashld=F        #  AP IAS HOLD FLAG
atst->lziasphd=F        #  IAS HOLD VIA PITCH A/P FLAG
atst->lziascf=F         #  IAS HOLD WITH CD SHIFT
atst->lzmachld=F        #  AP MACH HOLD FLAG
atst->lzfnhld=F         #  AP FN HOLD FLAG
atst->lzfnudothld=F     #  FN HLD WITH UDOT BIAS
atst->lzfniashld=F      #  FN HLD WITH IAS BIAS
atst->lzn1hld=F         #  AP N1 HOLD FLAG
flt->lztqhld=F          #  TORQUE HOLD
atst->lztqudothld=F     #  TORQUE HOLD WITH UDOT BIAS
atst->lztassist=F       #  TORQUE HOLD THORTTLE ASSIST
atst->lzeprhld=F        #  AP EPR HOLD FLAG
atst->lzreverse(1)=F    #  REVERSE THRUST FLAG
atst->lzreverse(2)=F    #  REVERSE THRUST FLAG
atst->lzbrknx=F         #  NX BRAKE HOLD
atst->lzbphld=F         #  BRAKE PRESS HOLD WITH PEDAL
atst->lzflare=F         #  FLARE MODE
#
#  BEGIN TEST
#  ----------
#
flt->rdatime=  0.00
flt->rdatend=120.00
atst->ldahisty=T
atst->ldaplot=T
atst->kdarate=1
#
atst->ldaprint=T
WAIT 1
flt->lfxfastm=T
flt->rfxpitcm=   3.16   # theta (deg)
flt->rfxrolcm=   1.10   # phi (deg)
flt->rfxhdgcm=  48.43+   flt->magvar   # hdg (deg)
#
WAIT 1
#
flt->dmd_cg_in=201.749     # Center of Gravity (in)
flt->fsetcg=T
flt->fsetgw=T
P '$AUTO/atg/trim_start.pag'
flt->ldatston=T
flt->frz=F
#
WAIT flt->ldatrimd T
WAIT atst->ldadoprt F
#
#  TEST COMPLETE
#  -------------
#
WAIT atst->ldatstip F
WAIT (atst->kdaiocmd == 0)
#
p '$AUTO/atg/tst_end.pag'
x
